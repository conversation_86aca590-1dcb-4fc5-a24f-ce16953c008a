// 主应用JavaScript
class QuarkApp {
    constructor() {
        this.logContainer = document.getElementById('log-container');
        this.statusText = document.getElementById('status-text');
        this.statusBadge = document.getElementById('status-badge');
        this.init();
    }

    async init() {
        await this.checkStatus();
        this.setupEventListeners();
        this.startStatusPolling();
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.status === 'ok') {
                if (data.hasCookie) {
                    this.updateStatus('系统就绪，Cookie已配置', 'success', '就绪');
                } else {
                    this.updateStatus('请先配置Cookie', 'warning', '需要配置');
                }
            } else {
                this.updateStatus('系统错误', 'error', '错误');
            }
        } catch (error) {
            this.updateStatus('连接失败', 'error', '离线');
            console.error('Status check failed:', error);
        }
    }

    updateStatus(text, type, badge) {
        this.statusText.textContent = text;
        this.statusBadge.textContent = badge;
        this.statusBadge.className = `badge bg-${this.getBootstrapColor(type)}`;
    }

    getBootstrapColor(type) {
        const colors = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return colors[type] || 'secondary';
    }

    setupEventListeners() {
        // 设置模态框事件
        const saveModal = new bootstrap.Modal(document.getElementById('saveModal'));
        const searchModal = new bootstrap.Modal(document.getElementById('searchModal'));
        const apiSearchModal = new bootstrap.Modal(document.getElementById('apiSearchModal'));

        window.showSaveDialog = () => saveModal.show();
        window.showSearchDialog = () => searchModal.show();
        window.showApiSearchDialog = () => apiSearchModal.show();

        // 管理模态框
        const manageModal = new bootstrap.Modal(document.getElementById('manageModal'));
        window.showManageModal = () => {
            manageModal.show();
            this.loadManageData();
        };

        // 设置全局函数
        window.executeAction = this.executeAction.bind(this);
        window.executeSave = this.executeSave.bind(this);
        window.executeSearch = this.executeSearch.bind(this);
        window.executeApiAction = this.executeApiAction.bind(this);
        window.executeApiSearch = this.executeApiSearch.bind(this);

        // 管理功能
        window.serviceAction = this.serviceAction.bind(this);
        window.taskAction = this.taskAction.bind(this);
        window.loadTasks = this.loadTasks.bind(this);
        window.openTaskScheduler = this.openTaskScheduler.bind(this);
        window.loadLogs = this.loadLogs.bind(this);
        window.cleanLogs = this.cleanLogs.bind(this);
        window.uploadFile = this.uploadFile.bind(this);

        // 金山文档功能
        window.syncToKdocs = this.syncToKdocs.bind(this);
        window.loadKdocsBackups = this.loadKdocsBackups.bind(this);
        window.viewKdocsContent = this.viewKdocsContent.bind(this);
        window.openKdocs = this.openKdocs.bind(this);
        window.copyContent = this.copyContent.bind(this);
        window.copyContentAndOpen = this.copyContentAndOpen.bind(this);
        window.copyLatestAndOpen = this.copyLatestAndOpen.bind(this);
        window.copyFileContentAndOpen = this.copyFileContentAndOpen.bind(this);
        window.autoSyncToKdocs = this.autoSyncToKdocs.bind(this);
    }

    async executeAction(action, params = {}) {
        this.addLog(`开始执行: ${this.getActionName(action)}`, 'info');
        this.showLoading(true);

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    params: params
                })
            });

            const data = await response.json();
            
            if (data.status === 'success') {
                this.addLog(`执行成功: ${this.getActionName(action)}`, 'success');
                this.addLog(data.output, 'info');
            } else {
                this.addLog(`执行失败: ${this.getActionName(action)}`, 'error');
                this.addLog(data.output, 'error');
            }
        } catch (error) {
            this.addLog(`网络错误: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async executeSave() {
        const fileInput = document.getElementById('fileInput');
        const filePathInput = document.getElementById('filePathInput');
        
        let filePath = filePathInput.value.trim();
        
        if (fileInput.files.length > 0) {
            // 如果选择了文件，使用文件名
            filePath = fileInput.files[0].name;
            // 这里可以添加文件上传逻辑
            this.addLog('注意: 文件上传功能需要将文件放在程序目录下', 'warning');
        }
        
        if (!filePath) {
            this.addLog('请选择文件或输入文件路径', 'error');
            return;
        }

        bootstrap.Modal.getInstance(document.getElementById('saveModal')).hide();
        await this.executeAction('save', { path: filePath });
    }

    async executeSearch() {
        const dramaNameInput = document.getElementById('dramaNameInput');
        const dramaName = dramaNameInput.value.trim();

        if (!dramaName) {
            this.addLog('请输入短剧名称', 'error');
            return;
        }

        bootstrap.Modal.getInstance(document.getElementById('searchModal')).hide();
        await this.executeAction('auto', { name: dramaName });
    }

    async executeApiAction(action) {
        const actionNames = {
            'api_today': '获取今日更新短剧',
            'api_list': '获取全部短剧列表',
            'api_auto_update': 'API自动更新'
        };

        this.addLog(`开始执行: ${actionNames[action] || action}`, 'info');
        this.showLoading(true);

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    params: { 'auto-save': 'true' }
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.addLog(`执行成功: ${actionNames[action] || action}`, 'success');
                this.addLog(data.output, 'info');

                // 显示桌面通知
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification('夸克工具', {
                        body: `${actionNames[action] || action} 执行完成`,
                        icon: '/favicon.ico'
                    });
                }
            } else {
                this.addLog(`执行失败: ${actionNames[action] || action}`, 'error');
                this.addLog(data.output, 'error');
            }
        } catch (error) {
            this.addLog(`网络错误: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async executeApiSearch() {
        const apiSearchInput = document.getElementById('apiSearchInput');
        const apiAutoSave = document.getElementById('apiAutoSave');
        const searchKeyword = apiSearchInput.value.trim();

        if (!searchKeyword) {
            this.addLog('请输入搜索关键词', 'error');
            return;
        }

        bootstrap.Modal.getInstance(document.getElementById('apiSearchModal')).hide();

        this.addLog(`开始API搜索: ${searchKeyword}`, 'info');
        this.showLoading(true);

        try {
            const params = {
                search: searchKeyword
            };

            if (apiAutoSave.checked) {
                params['auto-save'] = 'true';
            }

            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'api_search',
                    params: params
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.addLog(`API搜索成功: ${searchKeyword}`, 'success');
                this.addLog(data.output, 'info');

                // 显示桌面通知
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification('夸克工具', {
                        body: `搜索"${searchKeyword}"完成`,
                        icon: '/favicon.ico'
                    });
                }
            } else {
                this.addLog(`API搜索失败: ${searchKeyword}`, 'error');
                this.addLog(data.output, 'error');
            }
        } catch (error) {
            this.addLog(`网络错误: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
        
        this.logContainer.appendChild(logEntry);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
        
        // 限制日志条目数量
        const entries = this.logContainer.querySelectorAll('.log-entry');
        if (entries.length > 100) {
            entries[0].remove();
        }
    }

    showLoading(show) {
        const buttons = document.querySelectorAll('button[onclick^="executeAction"]');
        buttons.forEach(button => {
            if (show) {
                button.disabled = true;
                const originalText = button.innerHTML;
                button.dataset.originalText = originalText;
                button.innerHTML = '<span class="loading"></span> 执行中...';
            } else {
                button.disabled = false;
                if (button.dataset.originalText) {
                    button.innerHTML = button.dataset.originalText;
                }
            }
        });
    }

    getActionName(action) {
        const names = {
            'sign': '自动签到',
            'save': '转存资源',
            'share': '分享资源',
            'syn_dir': '同步目录',
            'auto': '自动更新'
        };
        return names[action] || action;
    }

    startStatusPolling() {
        // 每30秒检查一次状态
        setInterval(() => {
            this.checkStatus();
        }, 30000);
    }

    // 管理功能
    async loadManageData() {
        await this.loadServiceStatus();
        await this.loadTasks();
        await this.loadLogs();
        await this.loadFiles();
        await this.loadKdocsBackups();
    }

    async loadServiceStatus() {
        try {
            const response = await fetch('/api/service');
            const data = await response.json();

            const statusDiv = document.getElementById('service-status');
            if (data.status === 'success') {
                const status = data.isRunning ? '运行中' : '已停止';
                const color = data.isRunning ? 'success' : 'danger';
                statusDiv.innerHTML = `
                    <div class="alert alert-${color} mb-0">
                        <strong>状态:</strong> ${status}<br>
                        <strong>端口:</strong> ${data.port}
                    </div>
                `;
            } else {
                statusDiv.innerHTML = '<div class="alert alert-danger mb-0">无法获取服务状态</div>';
            }
        } catch (error) {
            console.error('Failed to load service status:', error);
        }
    }

    async serviceAction(action) {
        try {
            const response = await fetch('/api/service', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action })
            });

            const data = await response.json();
            this.addLog(data.message, data.status === 'success' ? 'success' : 'error');

            // 刷新服务状态
            setTimeout(() => this.loadServiceStatus(), 1000);
        } catch (error) {
            this.addLog(`服务操作失败: ${error.message}`, 'error');
        }
    }

    async loadTasks() {
        try {
            const response = await fetch('/api/tasks');
            const data = await response.json();

            const tasksDiv = document.getElementById('tasks-list');
            if (data.status === 'success' && data.tasks.length > 0) {
                tasksDiv.innerHTML = data.tasks.map(task => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>${task.name}</span>
                        <span class="badge bg-${task.status === 'Ready' ? 'success' : 'secondary'}">${task.status}</span>
                    </div>
                `).join('');
            } else {
                tasksDiv.innerHTML = '<p class="text-muted mb-0">暂无定时任务</p>';
            }

            // 检查权限状态
            this.checkAdminPermission();
        } catch (error) {
            console.error('Failed to load tasks:', error);
        }
    }

    // 检查管理员权限
    async checkAdminPermission() {
        try {
            const response = await fetch('/api/tasks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'check_permission' })
            });

            const data = await response.json();

            // 更新权限状态显示
            const alertDiv = document.querySelector('#tasks .alert-warning');
            if (alertDiv) {
                if (data.has_permission) {
                    alertDiv.className = 'alert alert-success';
                    alertDiv.innerHTML = `
                        <small>
                            <i class="bi bi-check-circle"></i>
                            <strong>权限状态：</strong>✅ 已具备管理员权限，可以创建定时任务
                        </small>
                    `;
                } else {
                    alertDiv.className = 'alert alert-warning';
                    // 保持原有的警告内容
                }
            }
        } catch (error) {
            console.error('Failed to check permission:', error);
        }
    }

    async taskAction(action) {
        try {
            const response = await fetch('/api/tasks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action })
            });

            const data = await response.json();
            this.addLog(data.message, data.status === 'success' ? 'success' : 'error');

            // 如果是权限错误，提供额外的帮助信息
            if (data.status === 'error' && data.message.includes('管理员权限')) {
                this.addLog('💡 解决方案：', 'info');
                this.addLog('1. 以管理员身份运行浏览器', 'info');
                this.addLog('2. 或者双击运行"创建定时任务.bat"文件', 'info');
                this.addLog('3. 或者点击"打开任务计划程序"手动创建', 'info');
            }

            // 刷新任务列表
            setTimeout(() => this.loadTasks(), 1000);
        } catch (error) {
            this.addLog(`任务操作失败: ${error.message}`, 'error');
        }
    }

    // 打开Windows任务计划程序
    openTaskScheduler() {
        // 尝试通过服务端打开任务计划程序
        fetch('/api/tasks', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'open_scheduler' })
        }).then(response => response.json())
        .then(data => {
            this.addLog(data.message, data.status === 'success' ? 'success' : 'info');
        }).catch(error => {
            this.addLog('无法自动打开任务计划程序，请手动运行 taskschd.msc', 'info');
        });
    }

    async loadLogs() {
        try {
            const response = await fetch('/api/logs');
            const data = await response.json();

            const logsDiv = document.getElementById('logs-list');
            if (data.status === 'success' && data.logs.length > 0) {
                logsDiv.innerHTML = data.logs.map(log => {
                    const date = new Date(log.modified * 1000).toLocaleString();
                    const size = this.formatFileSize(log.size);
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                            <div>
                                <strong>${log.name}</strong><br>
                                <small class="text-muted">${log.type} | ${date}</small>
                            </div>
                            <span class="badge bg-info">${size}</span>
                        </div>
                    `;
                }).join('');
            } else {
                logsDiv.innerHTML = '<p class="text-muted mb-0">暂无日志文件</p>';
            }
        } catch (error) {
            console.error('Failed to load logs:', error);
        }
    }

    async cleanLogs() {
        const days = document.getElementById('cleanDays').value;

        try {
            const response = await fetch('/api/files', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'clean_logs', days: parseInt(days) })
            });

            const data = await response.json();
            this.addLog(data.message, data.status === 'success' ? 'success' : 'error');

            // 刷新日志列表
            setTimeout(() => this.loadLogs(), 1000);
        } catch (error) {
            this.addLog(`清理日志失败: ${error.message}`, 'error');
        }
    }

    async loadFiles() {
        try {
            const response = await fetch('/api/files');
            const data = await response.json();

            const filesDiv = document.getElementById('files-list');
            if (data.status === 'success' && data.files.length > 0) {
                filesDiv.innerHTML = data.files.map(file => {
                    const date = new Date(file.modified * 1000).toLocaleString();
                    const size = this.formatFileSize(file.size);
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                            <div>
                                <strong>${file.name}</strong><br>
                                <small class="text-muted">${file.type.toUpperCase()} | ${date}</small>
                            </div>
                            <span class="badge bg-primary">${size}</span>
                        </div>
                    `;
                }).join('');
            } else {
                filesDiv.innerHTML = '<p class="text-muted mb-0">暂无资源文件</p>';
            }
        } catch (error) {
            console.error('Failed to load files:', error);
        }
    }

    async uploadFile() {
        const fileInput = document.getElementById('uploadFile');
        const file = fileInput.files[0];

        if (!file) {
            this.addLog('请选择要上传的文件', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('action', 'upload');

        try {
            const response = await fetch('/api/files', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            this.addLog(data.message, data.status === 'success' ? 'success' : 'error');

            if (data.status === 'success') {
                fileInput.value = '';
                setTimeout(() => this.loadFiles(), 1000);
            }
        } catch (error) {
            this.addLog(`文件上传失败: ${error.message}`, 'error');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 金山文档功能
    async syncToKdocs() {
        try {
            // 显示状态指示器
            this.showSyncStatus(true);
            this.addLog('开始生成同步内容...', 'info');

            const response = await fetch('/api/kdocs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'sync' })
            });

            const data = await response.json();
            this.addLog(data.message, data.status === 'success' ? 'success' : 'error');

            if (data.output) {
                this.addLog(data.output, 'info');
            }

            // 隐藏状态指示器
            this.showSyncStatus(false);

            if (data.status === 'success') {
                // 显示复制并打开按钮
                this.showCopyAndOpenButton(true);
                this.addLog('同步内容已生成，点击"复制内容并打开文档"按钮进行快速同步', 'success');
            }

            // 刷新备份列表
            setTimeout(() => this.loadKdocsBackups(), 1000);

        } catch (error) {
            this.showSyncStatus(false);
            this.addLog(`生成同步内容失败: ${error.message}`, 'error');
        }
    }

    async loadKdocsBackups() {
        try {
            const response = await fetch('/api/kdocs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'get_backup' })
            });

            const data = await response.json();

            const backupsDiv = document.getElementById('kdocs-backups');
            if (data.status === 'success' && data.backups.length > 0) {
                backupsDiv.innerHTML = data.backups.map((backup, index) => {
                    const date = new Date(backup.modified * 1000).toLocaleString();
                    const size = this.formatFileSize(backup.size);
                    const isLatest = index === 0;
                    return `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded ${isLatest ? 'border-success' : ''}">
                            <div>
                                <strong>${backup.name}</strong>
                                ${isLatest ? '<span class="badge bg-success ms-2">最新</span>' : ''}
                                <br>
                                <small class="text-muted">${date} | ${size}</small>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-success" onclick="copyFileContentAndOpen('${backup.name}')" title="复制内容并打开文档">
                                    <i class="bi bi-clipboard-check"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewKdocsContent('${backup.name}')" title="查看内容">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
            } else {
                backupsDiv.innerHTML = '<p class="text-muted mb-0">暂无备份文件</p>';
            }
        } catch (error) {
            console.error('Failed to load kdocs backups:', error);
        }
    }

    async viewKdocsContent(filename) {
        try {
            const response = await fetch('/api/kdocs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'get_content', filename })
            });

            const data = await response.json();

            if (data.status === 'success') {
                document.getElementById('contentFilename').value = data.filename;
                document.getElementById('contentText').value = data.content;

                const contentModal = new bootstrap.Modal(document.getElementById('contentModal'));
                contentModal.show();
            } else {
                this.addLog(`获取文件内容失败: ${data.message}`, 'error');
            }
        } catch (error) {
            this.addLog(`获取文件内容失败: ${error.message}`, 'error');
        }
    }

    openKdocs() {
        window.open('https://kdocs.cn/l/cqLsUjOoGFoV', '_blank');
    }

    copyContent() {
        const contentText = document.getElementById('contentText');
        contentText.select();
        document.execCommand('copy');

        // 显示复制成功提示
        this.addLog('内容已复制到剪贴板', 'success');

        // 显示临时提示
        const copyBtn = event.target;
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="bi bi-check"></i> 已复制';
        copyBtn.classList.remove('btn-primary');
        copyBtn.classList.add('btn-success');

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('btn-success');
            copyBtn.classList.add('btn-primary');
        }, 2000);
    }

    // 复制内容并打开文档
    copyContentAndOpen() {
        this.copyContent();
        setTimeout(() => {
            this.openKdocs();
            this.addLog('已复制内容并打开金山文档，请在文档中粘贴内容（Ctrl+V）', 'info');
        }, 500);
    }

    // 显示/隐藏同步状态
    showSyncStatus(show) {
        const statusDiv = document.getElementById('sync-status');
        const syncBtn = document.getElementById('sync-btn');

        if (show) {
            statusDiv.style.display = 'block';
            syncBtn.disabled = true;
            syncBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 生成中...';
        } else {
            statusDiv.style.display = 'none';
            syncBtn.disabled = false;
            syncBtn.innerHTML = '<i class="bi bi-cloud-upload"></i> 生成同步内容';
        }
    }

    // 显示/隐藏复制并打开按钮
    showCopyAndOpenButton(show) {
        const button = document.getElementById('copy-and-open-btn');
        button.style.display = show ? 'block' : 'none';
    }

    // 复制最新备份内容并打开文档
    async copyLatestAndOpen() {
        try {
            const response = await fetch('/api/kdocs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'get_latest' })
            });

            const data = await response.json();

            if (data.status === 'success' && data.content) {
                // 复制到剪贴板
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(data.content);
                } else {
                    // 创建临时文本区域
                    const textArea = document.createElement('textarea');
                    textArea.value = data.content;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                }

                this.addLog('已复制最新同步内容到剪贴板', 'success');

                // 打开文档
                setTimeout(() => {
                    this.openKdocs();
                    this.addLog('已打开金山文档，请在文档中粘贴内容（Ctrl+V）', 'info');
                }, 500);
            } else {
                this.addLog('没有找到最新的同步内容', 'error');
            }
        } catch (error) {
            this.addLog(`复制最新内容失败: ${error.message}`, 'error');
        }
    }

    // 复制指定文件内容并打开文档
    async copyFileContentAndOpen(filename) {
        try {
            const response = await fetch('/api/kdocs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'get_content', filename })
            });

            const data = await response.json();

            if (data.status === 'success' && data.content) {
                // 复制到剪贴板
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(data.content);
                } else {
                    // 创建临时文本区域
                    const textArea = document.createElement('textarea');
                    textArea.value = data.content;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                }

                this.addLog(`已复制 ${filename} 内容到剪贴板`, 'success');

                // 打开文档
                setTimeout(() => {
                    this.openKdocs();
                    this.addLog('已打开金山文档，请在文档中粘贴内容（Ctrl+V）', 'info');
                }, 500);
            } else {
                this.addLog('获取文件内容失败', 'error');
            }
        } catch (error) {
            this.addLog(`复制文件内容失败: ${error.message}`, 'error');
        }
    }

    // 自动同步到金山文档
    async autoSyncToKdocs() {
        try {
            // 显示状态指示器
            this.showAutoSyncStatus(true);
            this.addLog('开始自动同步到金山文档...', 'info');

            const response = await fetch('/api/kdocs', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'auto_sync' })
            });

            const data = await response.json();

            // 隐藏状态指示器
            this.showAutoSyncStatus(false);

            if (data.status === 'success') {
                this.addLog('🎉 自动同步成功！内容已自动添加到金山文档', 'success');

                // 显示成功提示
                this.showNotification('同步成功', '内容已自动同步到金山文档', 'success');
            } else {
                this.addLog(`自动同步失败: ${data.message}`, 'error');

                // 如果自动同步失败，提示用户使用手动同步
                if (data.message.includes('Python') || data.message.includes('自动化')) {
                    this.addLog('💡 提示：可以点击"生成同步内容"使用手动同步方式', 'info');
                }
            }

            if (data.output) {
                this.addLog(data.output, 'info');
            }

            // 刷新备份列表
            setTimeout(() => this.loadKdocsBackups(), 1000);

        } catch (error) {
            this.showAutoSyncStatus(false);
            this.addLog(`自动同步失败: ${error.message}`, 'error');
        }
    }

    // 显示/隐藏自动同步状态
    showAutoSyncStatus(show) {
        const autoSyncBtn = document.getElementById('auto-sync-btn');

        if (show) {
            autoSyncBtn.disabled = true;
            autoSyncBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 自动同步中...';
        } else {
            autoSyncBtn.disabled = false;
            autoSyncBtn.innerHTML = '<i class="bi bi-robot"></i> 自动同步（推荐）';
        }
    }

    // 显示通知
    showNotification(title, message, type = 'info') {
        // 如果支持浏览器通知
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: '/favicon.ico'
            });
        }

        // 在页面中显示提示
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' : 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <strong>${title}:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在日志区域上方显示
        const logContainer = document.querySelector('.log-container');
        if (logContainer) {
            logContainer.insertAdjacentHTML('beforebegin', alertHtml);

            // 3秒后自动移除
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new QuarkApp();
});

// 通知权限请求
function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
}

// 显示桌面通知
function showNotification(title, message, type = 'info') {
    if ('Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification(title, {
            body: message,
            icon: '/favicon.ico',
            tag: 'quark-app'
        });

        setTimeout(() => notification.close(), 5000);
    }
}
