<!DOCTYPE html>
<html>
<head>
    <title>Test Kdocs API</title>
</head>
<body>
    <h1>Test Kdocs API</h1>
    <button onclick="testGetBackup()">Test Get Backup</button>
    <button onclick="testSync()">Test Sync</button>
    <div id="result"></div>

    <script>
        async function testGetBackup() {
            try {
                const response = await fetch('/api/kdocs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'get_backup' })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        async function testSync() {
            try {
                const response = await fetch('/api/kdocs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'sync' })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
