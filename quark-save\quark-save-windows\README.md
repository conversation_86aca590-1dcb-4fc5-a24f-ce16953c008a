# Quark Cloud Disk Automation Tool - Enhanced Windows Version

## 🌟 Overview
This is an enhanced Windows version of the Quark Cloud Disk automation tool with advanced features including background service, web interface, scheduled tasks, and system tray support.

## 🚀 Quick Start

### Option 1: One-Click Setup (Recommended)
1. Run `一键安装.bat` as Administrator
2. Follow the setup wizard
3. Configure your cookie when prompted
4. Start using the tool!

### Option 2: Manual Setup

#### 1. Get Quark Cloud Disk Cookie
1. Open your browser and log in to Quark Cloud Disk (https://pan.quark.cn)
2. Press F12 to open Developer Tools
3. Switch to the "Network" tab
4. Refresh the page
5. Find any request in the request list and check the Cookie in the request headers
6. Copy the complete Cookie content
7. Save the Cookie content to the `cookie.txt` file in the program directory

#### 2. Choose Your Method
- **🎯 All-in-One Control**: Run `启动夸克工具.bat` (RECOMMENDED)
- **📱 Classic Menu**: Run `quark-save.bat`
- **⚡ Quick Background**: Run `启动夸克工具.bat` → Option 2

## 🎯 Features

### Core Functions
- **Auto Sign-in**: Automatically complete daily Quark Cloud Disk sign-in
- **Resource Saving**: Batch save resources from txt, csv, xlsx, xls files
- **Resource Sharing**: Batch share folders in cloud disk
- **Directory Sync**: Sync cloud disk directory structure to local
- **Drama API**: Get drama resources from cloud API and auto save
- **Search Function**: Search dramas by keywords and auto save

### Advanced Features
- **🌐 Web Interface**: Modern web UI accessible at http://127.0.0.1:8080
- **🔄 Background Service**: Run silently in background
- **📱 System Tray**: Minimize to system tray
- **🔍 Service Monitor**: Auto-restart if service crashes
- **⏰ Scheduled Tasks**: Automatic daily updates and maintenance
- **📊 Log Management**: Comprehensive logging and cleanup
- **🛠️ Status Monitoring**: Real-time service status checking

### File Format Support
Resource files should contain one entry per line:
```
Movie Name 1 https://pan.quark.cn/s/xxxxxx
Movie Name 2 https://pan.quark.cn/s/yyyyyy
```
Supported formats: txt, csv, xlsx, xls

## Important Notes

1. Must configure cookie.txt file before first use
2. Cookie has expiration time, please re-obtain if login fails
3. Save operations have frequency limits, recommend appropriate intervals
4. Program automatically handles network request intervals to avoid restrictions

## 📁 Simplified File Structure
```
quark-save-windows/
├── 启动夸克工具.bat          # 🎯 Main control center (ALL-IN-ONE)
├── quark-save.bat           # 📱 Original command-line interface
├── 每日自动更新.bat         # ⏰ Daily update task
├── 清理日志.bat             # 🧹 Log cleanup utility
├── 创建定时任务.bat         # ⚙️ Quick task setup
├── QuarkService.php         # 🔧 Core service engine
├── server.php               # 🌐 Web server
├── cookie.txt               # 🔑 Authentication config
├── web/                     # 🎨 Web interface files
├── php/                     # ⚡ PHP runtime
├── vendor/                  # 📦 Dependencies
└── logs/                    # 📊 Operation logs
```

### 🎯 **Main Scripts (Only 5 files!)**
1. **启动夸克工具.bat** - Complete control center with all features
2. **quark-save.bat** - Original command-line menu
3. **每日自动更新.bat** - Automated daily updates
4. **清理日志.bat** - Log management
5. **创建定时任务.bat** - Quick scheduled task setup

## Troubleshooting

### Q: Shows "Invalid cookie" error?
A: Re-obtain Cookie and update cookie.txt file

### Q: Save operation fails?
A: Check network connection, confirm links are valid, reduce operation frequency

### Q: Program won't start?
A: Ensure all files are complete, especially files in php directory

## Technical Support
For issues, please visit the project homepage: https://github.com/henggedaren/quark-save

## License
This project is licensed under the Apache-2.0 License.
