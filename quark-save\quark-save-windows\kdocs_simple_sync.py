#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金山文档简化同步脚本
不依赖Selenium，使用系统剪贴板和浏览器自动化
"""

import os
import sys
import time
import webbrowser
import subprocess
import argparse
from datetime import datetime

try:
    import pyperclip
except ImportError:
    print("❌ 缺少 pyperclip 模块，正在尝试安装...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyperclip"])
        import pyperclip
        print("✅ pyperclip 安装成功")
    except Exception as e:
        print(f"❌ 无法安装 pyperclip: {e}")
        sys.exit(1)

class KdocsSimpleSync:
    def __init__(self):
        self.doc_url = "https://kdocs.cn/l/cqLsUjOoGFoV"
    
    def get_latest_backup_content(self):
        """获取最新的备份文件内容"""
        try:
            backup_dir = os.path.join(os.path.dirname(__file__), 'kdocs_backup')
            if not os.path.exists(backup_dir):
                print("❌ 备份目录不存在")
                return None
            
            # 查找最新的.md文件
            md_files = [f for f in os.listdir(backup_dir) if f.endswith('.md')]
            if not md_files:
                print("❌ 没有找到备份文件")
                return None
            
            # 按修改时间排序，获取最新的
            latest_file = max(md_files, key=lambda f: os.path.getmtime(os.path.join(backup_dir, f)))
            latest_path = os.path.join(backup_dir, latest_file)
            
            with open(latest_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 读取最新备份文件: {latest_file}")
            return content
            
        except Exception as e:
            print(f"❌ 读取备份文件失败: {e}")
            return None
    
    def copy_to_clipboard(self, content):
        """复制内容到剪贴板"""
        try:
            pyperclip.copy(content)
            print("📋 内容已复制到剪贴板")
            return True
        except Exception as e:
            print(f"❌ 复制到剪贴板失败: {e}")
            return False
    
    def open_document(self):
        """打开金山文档"""
        try:
            print(f"🌐 正在打开金山文档: {self.doc_url}")
            webbrowser.open(self.doc_url)
            return True
        except Exception as e:
            print(f"❌ 打开文档失败: {e}")
            return False
    
    def show_instructions(self):
        """显示操作说明"""
        print("\n" + "="*50)
        print("📝 手动粘贴说明")
        print("="*50)
        print("1. 内容已复制到剪贴板")
        print("2. 金山文档将在浏览器中打开")
        print("3. 请在文档中找到合适位置")
        print("4. 按 Ctrl+V 粘贴内容")
        print("5. 保存文档")
        print("="*50)
    
    def auto_sync(self, content=None):
        """执行自动同步"""
        try:
            print("🚀 开始简化自动同步...")
            
            # 获取内容
            if content:
                sync_content = content
            else:
                sync_content = self.get_latest_backup_content()
                if not sync_content:
                    return False
            
            # 复制到剪贴板
            if not self.copy_to_clipboard(sync_content):
                return False
            
            # 打开文档
            if not self.open_document():
                return False
            
            # 显示说明
            self.show_instructions()
            
            print("✅ 简化自动同步完成！")
            print("💡 请在浏览器中手动粘贴内容")
            
            return True
            
        except Exception as e:
            print(f"❌ 简化自动同步失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='金山文档简化同步工具')
    parser.add_argument('--content', type=str, help='直接指定要同步的内容')
    
    args = parser.parse_args()
    
    sync_tool = KdocsSimpleSync()
    
    try:
        success = sync_tool.auto_sync(args.content)
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
