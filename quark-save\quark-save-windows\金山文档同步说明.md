# 🌐 金山文档自动同步功能

## 📋 功能概述

现在您的夸克网盘自动化工具支持将每天转存分享后的链接**自动同步到金山文档**！

目标文档：[火星生活科技短剧库](https://kdocs.cn/l/cqLsUjOoGFoV)

## ✨ 主要特性

### 🔄 自动同步
- **分享后自动同步**：每次执行分享操作后，自动将分享链接同步到金山文档
- **格式化输出**：生成美观的表格格式，包含序号、短剧名称、分享链接、添加时间
- **本地备份**：所有同步内容都会保存本地备份文件

### 📱 Web界面管理
- **可视化操作**：通过Web界面一键同步到金山文档
- **备份文件管理**：查看、预览所有同步备份文件
- **内容复制**：一键复制同步内容到剪贴板
- **直接跳转**：快速打开金山文档进行手动粘贴

### 💾 本地备份
- **自动备份**：每次同步都会生成本地备份文件
- **文件命名**：`kdocs_sync_YYYY-MM-DD_HH-mm-ss.md`
- **手动指南**：自动生成详细的手动同步指南

## 🚀 使用方法

### 方法1：自动同步（推荐）
```bash
# 执行分享操作，会自动同步到金山文档
php\php.exe QuarkService.php --options share
```

### 方法2：手动同步
```bash
# 单独执行金山文档同步
php\php.exe QuarkService.php --options sync_kdocs
```

### 方法3：Web界面同步
1. 打开Web界面：http://127.0.0.1:8080
2. 点击导航栏"管理"按钮
3. 选择"金山文档"选项卡
4. 点击"同步到金山文档"按钮

## 📊 同步内容格式

生成的内容格式如下：

```markdown
## 2025年06月16日 新增短剧

| 序号 | 短剧名称 | 分享链接 | 添加时间 |
|------|----------|----------|----------|
| 1 | 短剧名称1 | https://pan.quark.cn/s/xxx | 21:30:15 |
| 2 | 短剧名称2 | https://pan.quark.cn/s/yyy | 21:30:16 |
| 3 | 短剧名称3 | https://pan.quark.cn/s/zzz | 21:30:17 |

---
*自动同步时间：2025-06-16 21:30:15*
```

## 📁 文件结构

```
quark-save-windows/
├── KdocsSync.php              # 金山文档同步核心类
├── kdocs_backup/              # 同步备份目录
│   ├── kdocs_sync_*.md        # 同步内容备份
│   └── manual_guide_*.txt     # 手动同步指南
└── logs/system/               # 同步日志
    └── kdocs_sync_*.log       # 同步操作日志
```

## 🔧 配置选项

### 修改目标文档
如需修改目标金山文档，编辑 `KdocsSync.php` 文件：

```php
// 修改这行的URL
$this->docUrl = $docUrl ?: 'https://kdocs.cn/l/你的文档ID';
```

### Web界面配置
在 `web/index.html` 中修改：

```html
<input type="text" class="form-control" id="kdocsUrl" value="https://kdocs.cn/l/你的文档ID">
```

## 🛠️ 故障排除

### 同步失败
1. **检查网络连接**：确保能访问金山文档
2. **查看日志**：检查 `logs/system/kdocs_sync_*.log` 文件
3. **手动同步**：使用生成的备份文件手动复制到金山文档

### 权限问题
1. **文档权限**：确保对目标金山文档有编辑权限
2. **文件权限**：确保程序对 `kdocs_backup` 目录有写入权限

### API限制
由于金山文档可能没有公开的写入API，目前采用以下方案：
1. **生成格式化内容**：自动生成标准格式的同步内容
2. **本地备份**：保存所有同步内容到本地
3. **手动复制**：提供便捷的复制粘贴功能

## 📈 使用流程

### 完整工作流程
1. **转存短剧** → 2. **同步目录** → 3. **分享资源** → 4. **自动同步到金山文档**

### 命令示例
```bash
# 完整流程
php\php.exe QuarkService.php --options save --path resources.txt
php\php.exe QuarkService.php --options syn_dir
php\php.exe QuarkService.php --options share
# 分享完成后会自动同步到金山文档

# 或使用API自动更新（包含自动同步）
php\php.exe QuarkService.php --options api_auto_update
```

## 🎯 最佳实践

1. **定期备份**：定期备份 `kdocs_backup` 目录
2. **检查同步**：每次同步后检查金山文档是否更新成功
3. **清理日志**：定期清理旧的日志和备份文件
4. **权限管理**：确保金山文档的编辑权限设置正确

## 🔮 未来计划

- [ ] 支持更多在线文档平台（腾讯文档、石墨文档等）
- [ ] 实现真正的API自动同步（如果平台提供API）
- [ ] 添加同步状态监控和通知
- [ ] 支持自定义同步格式模板

---

**享受自动化的便利！** 🚀
