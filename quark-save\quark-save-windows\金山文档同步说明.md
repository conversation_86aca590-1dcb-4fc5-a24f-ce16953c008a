# 🌐 金山文档自动同步功能

## 📋 功能概述

现在您的夸克网盘自动化工具支持**多种方式**将每天转存分享后的链接同步到金山文档！

目标文档：[火星生活科技短剧库](https://kdocs.cn/l/cqLsUjOoGFoV)

## ✨ 三种同步方案

### 🤖 方案一：自动同步（推荐）
- **简化自动同步**：自动复制内容到剪贴板并打开文档，只需手动粘贴
- **完整自动化**：使用浏览器自动化技术，完全无需手动操作
- **智能选择**：系统自动选择最适合的同步方式

### 📋 方案二：手动同步（兼容性最好）
- **一键复制**：生成格式化内容并复制到剪贴板
- **自动打开**：自动打开金山文档页面
- **便捷操作**：只需在文档中粘贴内容

### 💾 方案三：本地备份
- **自动备份**：每次同步都会生成本地备份文件
- **文件管理**：通过Web界面查看和管理所有备份
- **手动指南**：自动生成详细的手动同步指南

## 🚀 主要特性

### 🔄 智能同步
- **分享后自动同步**：每次执行分享操作后，自动将分享链接同步到金山文档
- **多种同步方式**：根据环境自动选择最佳同步方案
- **格式化输出**：生成美观的表格格式，包含序号、短剧名称、分享链接、添加时间
- **本地备份**：所有同步内容都会保存本地备份文件

### 📱 Web界面管理
- **可视化操作**：通过Web界面选择不同的同步方式
- **状态指示**：实时显示同步进度和状态
- **备份文件管理**：查看、预览所有同步备份文件
- **一键操作**：复制内容、打开文档、自动同步等功能
- **智能提示**：根据环境提供最佳使用建议

## 🚀 使用方法

### 🎯 推荐使用流程

#### 第一步：安装自动同步依赖（首次使用）
```bash
# 运行安装脚本
install_auto_sync.bat
```

#### 第二步：选择同步方式

**方式1：Web界面自动同步（最简单）**
1. 打开Web界面：http://127.0.0.1:8080
2. 点击导航栏"管理"按钮
3. 选择"金山文档"选项卡
4. 点击"自动同步（推荐）"按钮
5. 首次使用时在浏览器中登录金山文档
6. 系统自动完成同步或提示手动粘贴

**方式2：命令行自动同步**
```bash
# 执行分享操作，会自动同步到金山文档
php\php.exe QuarkService.php --options share
```

**方式3：手动同步**
```bash
# 单独执行金山文档同步
php\php.exe QuarkService.php --options sync_kdocs
```

### 📋 详细操作步骤

#### 自动同步步骤
1. **简化自动同步**（推荐）：
   - 点击"自动同步"按钮
   - 内容自动复制到剪贴板
   - 金山文档自动在浏览器中打开
   - 在文档中按 Ctrl+V 粘贴内容
   - 保存文档

2. **完整自动化**（需要Selenium）：
   - 点击"自动同步"按钮
   - 系统自动打开浏览器
   - 自动登录并编辑文档
   - 完全无需手动操作

#### 手动同步步骤
1. 点击"生成同步内容"按钮
2. 点击"复制内容并打开文档"按钮
3. 在打开的文档中粘贴内容（Ctrl+V）
4. 保存文档

## 📊 同步内容格式

生成的内容格式如下：

```markdown
## 2025年06月16日 新增短剧

| 序号 | 短剧名称 | 分享链接 | 添加时间 |
|------|----------|----------|----------|
| 1 | 短剧名称1 | https://pan.quark.cn/s/xxx | 21:30:15 |
| 2 | 短剧名称2 | https://pan.quark.cn/s/yyy | 21:30:16 |
| 3 | 短剧名称3 | https://pan.quark.cn/s/zzz | 21:30:17 |

---
*自动同步时间：2025-06-16 21:30:15*
```

## 📁 文件结构

```
quark-save-windows/
├── KdocsSync.php              # 金山文档同步核心类
├── kdocs_simple_sync.py       # 简化自动同步脚本（推荐）
├── kdocs_auto_sync.py         # 完整自动化同步脚本
├── install_auto_sync.bat      # 自动同步依赖安装脚本
├── requirements.txt           # Python依赖列表
├── kdocs_backup/              # 同步备份目录
│   ├── kdocs_sync_*.md        # 同步内容备份
│   └── manual_guide_*.txt     # 手动同步指南
└── logs/system/               # 同步日志
    └── kdocs_sync_*.log       # 同步操作日志
```

## 🔧 配置选项

### 修改目标文档
如需修改目标金山文档，编辑 `KdocsSync.php` 文件：

```php
// 修改这行的URL
$this->docUrl = $docUrl ?: 'https://kdocs.cn/l/你的文档ID';
```

### Web界面配置
在 `web/index.html` 中修改：

```html
<input type="text" class="form-control" id="kdocsUrl" value="https://kdocs.cn/l/你的文档ID">
```

## 🛠️ 故障排除

### 自动同步问题

#### Python环境问题
1. **Python未安装**：
   - 下载并安装Python 3.7+：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **依赖包缺失**：
   ```bash
   # 运行安装脚本
   install_auto_sync.bat

   # 或手动安装
   pip install pyperclip
   ```

3. **权限问题**：
   - 以管理员身份运行命令提示符
   - 重新安装依赖包

#### 浏览器自动化问题
1. **Chrome浏览器未安装**：
   - 下载安装Chrome：https://www.google.com/chrome/

2. **ChromeDriver问题**：
   - 运行安装脚本会自动下载
   - 或手动下载：https://chromedriver.chromium.org/

3. **登录问题**：
   - 首次使用需要手动登录金山文档
   - 登录信息会保存在浏览器中

### 手动同步问题

#### 同步失败
1. **检查网络连接**：确保能访问金山文档
2. **查看日志**：检查 `logs/system/kdocs_sync_*.log` 文件
3. **备份恢复**：使用生成的备份文件手动复制到金山文档

#### 权限问题
1. **文档权限**：确保对目标金山文档有编辑权限
2. **文件权限**：确保程序对 `kdocs_backup` 目录有写入权限

#### 剪贴板问题
1. **复制失败**：
   - 检查是否有其他程序占用剪贴板
   - 重新点击复制按钮
   - 手动选择文本复制

### 通用解决方案
1. **重启服务**：重启Web服务器
2. **清理缓存**：删除浏览器缓存
3. **检查防火墙**：确保端口8080未被阻止
4. **查看日志**：检查详细错误信息

## 📈 使用流程

### 完整工作流程
1. **转存短剧** → 2. **同步目录** → 3. **分享资源** → 4. **自动同步到金山文档**

### 命令示例
```bash
# 完整流程
php\php.exe QuarkService.php --options save --path resources.txt
php\php.exe QuarkService.php --options syn_dir
php\php.exe QuarkService.php --options share
# 分享完成后会自动同步到金山文档

# 或使用API自动更新（包含自动同步）
php\php.exe QuarkService.php --options api_auto_update
```

## 🎯 最佳实践

1. **定期备份**：定期备份 `kdocs_backup` 目录
2. **检查同步**：每次同步后检查金山文档是否更新成功
3. **清理日志**：定期清理旧的日志和备份文件
4. **权限管理**：确保金山文档的编辑权限设置正确

## 🔮 未来计划

### 已完成 ✅
- [x] 实现简化自动同步（复制到剪贴板+自动打开文档）
- [x] 实现完整浏览器自动化同步
- [x] 多种同步方案智能选择
- [x] 改进的Web界面和用户体验
- [x] 详细的安装和使用指南

### 计划中 📋
- [ ] 支持更多在线文档平台（腾讯文档、石墨文档等）
- [ ] 实现金山文档官方API同步（当API可用时）
- [ ] 添加同步状态监控和通知
- [ ] 支持自定义同步格式模板
- [ ] 添加同步历史记录和版本管理
- [ ] 支持批量文档同步
- [ ] 添加同步冲突检测和解决

---

**享受自动化的便利！** 🚀
