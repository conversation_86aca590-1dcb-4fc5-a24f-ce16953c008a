# 🌐 金山文档自动同步功能

## 📋 功能概述

现在您的夸克网盘自动化工具支持**自动同步**到金山文档，不再需要手动复制粘贴！

目标文档：[火星生活科技短剧库](https://kdocs.cn/l/cqLsUjOoGFoV)

## ✨ 同步方案

### 🤖 自动同步（推荐）
- **一键操作**：点击按钮即可完成同步
- **自动复制**：内容自动复制到剪贴板
- **自动打开**：金山文档自动在浏览器中打开
- **简单粘贴**：只需在文档中按 Ctrl+V 粘贴

### 📋 手动同步（备选）
- **生成内容**：自动生成格式化的同步内容
- **一键复制**：复制内容并打开文档
- **兼容性好**：适用于所有环境

### 💾 本地备份
- **自动备份**：每次同步都会生成本地备份文件
- **文件管理**：通过Web界面查看和管理所有备份
- **快速操作**：直接从备份列表复制和同步

## 🚀 主要特性

### 🔄 智能同步
- **分享后自动同步**：每次执行分享操作后，自动将分享链接同步到金山文档
- **多种同步方式**：根据环境自动选择最佳同步方案
- **格式化输出**：生成美观的表格格式，包含序号、短剧名称、分享链接、添加时间
- **本地备份**：所有同步内容都会保存本地备份文件

### 📱 Web界面管理
- **可视化操作**：通过Web界面选择不同的同步方式
- **状态指示**：实时显示同步进度和状态
- **备份文件管理**：查看、预览所有同步备份文件
- **一键操作**：复制内容、打开文档、自动同步等功能
- **智能提示**：根据环境提供最佳使用建议

## 🚀 使用方法

### 🎯 快速开始

#### 第一步：启动工具
```bash
# 双击运行启动脚本（会自动检查和安装依赖）
启动工具.bat
```

#### 第二步：使用自动同步
1. 打开Web界面：http://127.0.0.1:8080
2. 点击导航栏"管理"按钮
3. 选择"金山文档"选项卡
4. 点击"自动同步（推荐）"按钮
5. 在自动打开的文档中按 Ctrl+V 粘贴内容

### 📋 其他使用方式

**命令行自动同步**
```bash
# 执行分享操作，会自动同步到金山文档
php\php.exe QuarkService.php --options share
```

**手动同步**
```bash
# 单独执行金山文档同步
php\php.exe QuarkService.php --options sync_kdocs
```

### 💡 操作说明

#### 自动同步（推荐）
1. 点击"自动同步"按钮
2. 内容自动复制到剪贴板
3. 金山文档自动在浏览器中打开
4. 在文档中按 Ctrl+V 粘贴内容

#### 手动同步
1. 点击"生成同步内容"按钮
2. 点击"复制内容并打开文档"按钮
3. 在文档中粘贴内容（Ctrl+V）

## 📊 同步内容格式

生成的内容格式如下：

```markdown
## 2025年06月16日 新增短剧

| 序号 | 短剧名称 | 分享链接 | 添加时间 |
|------|----------|----------|----------|
| 1 | 短剧名称1 | https://pan.quark.cn/s/xxx | 21:30:15 |
| 2 | 短剧名称2 | https://pan.quark.cn/s/yyy | 21:30:16 |
| 3 | 短剧名称3 | https://pan.quark.cn/s/zzz | 21:30:17 |

---
*自动同步时间：2025-06-16 21:30:15*
```

## 📁 文件结构

```
quark-save-windows/
├── KdocsSync.php              # 金山文档同步核心类
├── kdocs_sync.py              # 统一自动同步脚本
├── 启动工具.bat               # 启动脚本（自动检查依赖）
├── kdocs_backup/              # 同步备份目录
│   ├── kdocs_sync_*.md        # 同步内容备份
│   └── manual_guide_*.txt     # 手动同步指南
└── logs/system/               # 同步日志
    └── kdocs_sync_*.log       # 同步操作日志
```

## 🔧 配置选项

### 修改目标文档
如需修改目标金山文档，编辑 `KdocsSync.php` 文件：

```php
// 修改这行的URL
$this->docUrl = $docUrl ?: 'https://kdocs.cn/l/你的文档ID';
```

### Web界面配置
在 `web/index.html` 中修改：

```html
<input type="text" class="form-control" id="kdocsUrl" value="https://kdocs.cn/l/你的文档ID">
```

## 🛠️ 故障排除

### 常见问题

#### 自动同步失败
1. **Python环境问题**：
   - 重新运行 `启动工具.bat`，会自动检查和安装依赖
   - 如果仍有问题，手动安装Python：https://www.python.org/downloads/

2. **网络问题**：
   - 确保能访问金山文档网站
   - 检查防火墙设置

3. **权限问题**：
   - 确保对目标金山文档有编辑权限
   - 以管理员身份运行程序

#### 复制粘贴问题
1. **剪贴板失败**：
   - 重新点击复制按钮
   - 检查是否有其他程序占用剪贴板

2. **文档打开失败**：
   - 手动打开金山文档链接
   - 检查浏览器设置

### 解决方案
1. **查看日志**：检查 `logs/system/kdocs_sync_*.log` 文件
2. **重启服务**：重新运行 `启动工具.bat`
3. **使用备份**：从备份列表中手动复制内容
4. **联系支持**：如果问题持续存在

## 📈 使用流程

### 完整工作流程
1. **转存短剧** → 2. **同步目录** → 3. **分享资源** → 4. **自动同步到金山文档**

### 命令示例
```bash
# 完整流程
php\php.exe QuarkService.php --options save --path resources.txt
php\php.exe QuarkService.php --options syn_dir
php\php.exe QuarkService.php --options share
# 分享完成后会自动同步到金山文档

# 或使用API自动更新（包含自动同步）
php\php.exe QuarkService.php --options api_auto_update
```

## 🎯 最佳实践

1. **定期备份**：定期备份 `kdocs_backup` 目录
2. **检查同步**：每次同步后检查金山文档是否更新成功
3. **清理日志**：定期清理旧的日志和备份文件
4. **权限管理**：确保金山文档的编辑权限设置正确

## 🔮 未来计划

### 已完成 ✅
- [x] 实现简化自动同步（复制到剪贴板+自动打开文档）
- [x] 实现完整浏览器自动化同步
- [x] 多种同步方案智能选择
- [x] 改进的Web界面和用户体验
- [x] 详细的安装和使用指南

### 计划中 📋
- [ ] 支持更多在线文档平台（腾讯文档、石墨文档等）
- [ ] 实现金山文档官方API同步（当API可用时）
- [ ] 添加同步状态监控和通知
- [ ] 支持自定义同步格式模板
- [ ] 添加同步历史记录和版本管理
- [ ] 支持批量文档同步
- [ ] 添加同步冲突检测和解决

---

**享受自动化的便利！** 🚀
