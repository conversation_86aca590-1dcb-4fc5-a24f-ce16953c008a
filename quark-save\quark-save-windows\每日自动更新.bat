@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    if "%1" neq "auto" pause
    exit /b 1
)

echo ========================================
echo         Daily Auto Update
echo ========================================
echo Start: %date% %time%
echo.

"%PHP_PATH%" QuarkService.php --options api_auto_update

if %errorlevel% equ 0 (
    echo Update completed successfully.
) else (
    echo Update failed.
)

echo End: %date% %time%
echo.

if "%1" neq "auto" pause
