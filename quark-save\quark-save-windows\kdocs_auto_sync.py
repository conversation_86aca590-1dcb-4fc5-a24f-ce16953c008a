#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金山文档自动同步脚本
使用Selenium WebDriver自动化浏览器操作，实现真正的自动同步
"""

import os
import sys
import time
import json
import argparse
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class KdocsAutoSync:
    def __init__(self, headless=False, timeout=30):
        """
        初始化自动同步器
        
        Args:
            headless (bool): 是否使用无头模式
            timeout (int): 等待超时时间（秒）
        """
        self.timeout = timeout
        self.driver = None
        self.wait = None
        self.doc_url = "https://kdocs.cn/l/cqLsUjOoGFoV"
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')
            
            # 添加其他选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 设置用户数据目录以保持登录状态
            user_data_dir = os.path.join(os.path.dirname(__file__), 'chrome_user_data')
            chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            print("✅ Chrome WebDriver 初始化成功")
            
        except Exception as e:
            print(f"❌ Chrome WebDriver 初始化失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            sys.exit(1)
    
    def login_if_needed(self):
        """如果需要的话进行登录"""
        try:
            # 打开金山文档
            print(f"🌐 正在打开金山文档: {self.doc_url}")
            self.driver.get(self.doc_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 检查是否需要登录
            if "login" in self.driver.current_url.lower() or "登录" in self.driver.page_source:
                print("🔐 检测到需要登录，请在浏览器中手动登录...")
                print("登录完成后，请按回车键继续...")
                input()
                
                # 重新打开文档
                self.driver.get(self.doc_url)
                time.sleep(3)
            
            print("✅ 文档访问成功")
            return True
            
        except Exception as e:
            print(f"❌ 访问文档失败: {e}")
            return False
    
    def sync_content(self, content):
        """
        同步内容到金山文档
        
        Args:
            content (str): 要同步的内容
            
        Returns:
            bool: 同步是否成功
        """
        try:
            print("📝 开始同步内容到金山文档...")
            
            # 等待文档编辑器加载
            print("⏳ 等待文档编辑器加载...")
            
            # 尝试多种可能的编辑器选择器
            editor_selectors = [
                "div[contenteditable='true']",
                ".ql-editor",
                ".editor-content",
                "textarea",
                "#editor",
                ".document-editor"
            ]
            
            editor = None
            for selector in editor_selectors:
                try:
                    editor = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    print(f"✅ 找到编辑器: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not editor:
                print("❌ 未找到文档编辑器")
                return False
            
            # 滚动到文档底部
            print("📜 滚动到文档底部...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)
            
            # 点击编辑器以获得焦点
            editor.click()
            time.sleep(1)
            
            # 移动到文档末尾
            editor.send_keys(Keys.CONTROL + Keys.END)
            time.sleep(1)
            
            # 添加换行并插入内容
            editor.send_keys(Keys.ENTER)
            editor.send_keys(content)
            
            print("✅ 内容已插入到文档")
            
            # 等待一下让内容保存
            time.sleep(2)
            
            # 尝试手动保存（如果有保存按钮）
            try:
                save_button = self.driver.find_element(By.CSS_SELECTOR, "button[title*='保存'], button:contains('保存'), .save-btn")
                save_button.click()
                print("💾 已点击保存按钮")
            except NoSuchElementException:
                # 使用快捷键保存
                editor.send_keys(Keys.CONTROL + "s")
                print("💾 已使用快捷键保存")
            
            time.sleep(2)
            print("✅ 内容同步完成")
            return True
            
        except Exception as e:
            print(f"❌ 同步内容失败: {e}")
            return False
    
    def get_latest_backup_content(self):
        """获取最新的备份文件内容"""
        try:
            backup_dir = os.path.join(os.path.dirname(__file__), 'kdocs_backup')
            if not os.path.exists(backup_dir):
                print("❌ 备份目录不存在")
                return None
            
            # 查找最新的.md文件
            md_files = [f for f in os.listdir(backup_dir) if f.endswith('.md')]
            if not md_files:
                print("❌ 没有找到备份文件")
                return None
            
            # 按修改时间排序，获取最新的
            latest_file = max(md_files, key=lambda f: os.path.getmtime(os.path.join(backup_dir, f)))
            latest_path = os.path.join(backup_dir, latest_file)
            
            with open(latest_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 读取最新备份文件: {latest_file}")
            return content
            
        except Exception as e:
            print(f"❌ 读取备份文件失败: {e}")
            return None
    
    def auto_sync(self):
        """执行自动同步"""
        try:
            print("🚀 开始自动同步...")
            
            # 登录检查
            if not self.login_if_needed():
                return False
            
            # 获取最新备份内容
            content = self.get_latest_backup_content()
            if not content:
                return False
            
            # 同步内容
            success = self.sync_content(content)
            
            if success:
                print("🎉 自动同步完成！")
            else:
                print("❌ 自动同步失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 自动同步过程中出错: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("🔒 浏览器已关闭")

def main():
    parser = argparse.ArgumentParser(description='金山文档自动同步工具')
    parser.add_argument('--headless', action='store_true', help='使用无头模式运行')
    parser.add_argument('--timeout', type=int, default=30, help='等待超时时间（秒）')
    parser.add_argument('--content', type=str, help='直接指定要同步的内容')
    
    args = parser.parse_args()
    
    sync_tool = KdocsAutoSync(headless=args.headless, timeout=args.timeout)
    
    try:
        if args.content:
            # 同步指定内容
            sync_tool.login_if_needed()
            success = sync_tool.sync_content(args.content)
        else:
            # 自动同步最新备份
            success = sync_tool.auto_sync()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)
    finally:
        sync_tool.close()

if __name__ == "__main__":
    main()
