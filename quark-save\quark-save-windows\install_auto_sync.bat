@echo off
chcp 65001 >nul
echo ========================================
echo 金山文档自动同步工具安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 正在安装Python依赖包...
echo 首先安装基础依赖（简化同步）...
pip install pyperclip

if %errorlevel% neq 0 (
    echo ⚠️  基础依赖安装失败，尝试使用国内镜像源...
    pip install pyperclip -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if %errorlevel% neq 0 (
        echo ❌ 基础依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo ✅ 基础依赖安装完成（简化同步可用）

echo.
echo 是否安装完整自动化依赖（Selenium）？[Y/N]
set /p install_full="这将需要更多时间和空间，但可以实现完全自动化: "

if /i "%install_full%"=="Y" (
    echo 正在安装完整自动化依赖...
    pip install selenium webdriver-manager
    if %errorlevel% neq 0 (
        echo ⚠️  完整依赖安装失败，尝试使用国内镜像源...
        pip install selenium webdriver-manager -i https://pypi.tuna.tsinghua.edu.cn/simple/
        if %errorlevel% neq 0 (
            echo ⚠️  完整依赖安装失败，但简化同步仍可使用
        ) else (
            echo ✅ 完整依赖安装完成
        )
    ) else (
        echo ✅ 完整依赖安装完成
    )
) else (
    echo ℹ️  跳过完整自动化依赖安装，仅使用简化同步
)

echo.
echo 正在检查Chrome浏览器...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未检测到Chrome浏览器
    echo 请安装Chrome浏览器以使用自动同步功能
    echo 下载地址: https://www.google.com/chrome/
) else (
    echo ✅ Chrome浏览器检查通过
)

echo.
echo 正在下载ChromeDriver...
python -c "from selenium import webdriver; from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"

if %errorlevel% neq 0 (
    echo ⚠️  ChromeDriver下载失败，将在首次运行时自动下载
) else (
    echo ✅ ChromeDriver安装完成
)

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 使用方法:
echo 1. 简化同步（推荐）: python kdocs_simple_sync.py
echo 2. 完整自动化: python kdocs_auto_sync.py
echo 3. 在Web界面中点击"自动同步"按钮
echo.
echo 💡 简化同步说明:
echo    - 自动复制内容到剪贴板
echo    - 自动打开金山文档
echo    - 需要手动粘贴内容（Ctrl+V）
echo.
echo 💡 完整自动化说明:
echo    - 完全自动化，无需手动操作
echo    - 首次使用需要在浏览器中登录
echo    - 需要Chrome浏览器和ChromeDriver
echo.
pause
