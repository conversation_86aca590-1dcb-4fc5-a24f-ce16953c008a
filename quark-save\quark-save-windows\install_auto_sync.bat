@echo off
chcp 65001 >nul
echo ========================================
echo 金山文档自动同步工具安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 正在安装Python依赖包...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    echo 请检查网络连接或尝试使用国内镜像源:
    echo pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo.
echo 正在检查Chrome浏览器...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未检测到Chrome浏览器
    echo 请安装Chrome浏览器以使用自动同步功能
    echo 下载地址: https://www.google.com/chrome/
) else (
    echo ✅ Chrome浏览器检查通过
)

echo.
echo 正在下载ChromeDriver...
python -c "from selenium import webdriver; from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"

if %errorlevel% neq 0 (
    echo ⚠️  ChromeDriver下载失败，将在首次运行时自动下载
) else (
    echo ✅ ChromeDriver安装完成
)

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 使用方法:
echo 1. 手动同步: python kdocs_auto_sync.py
echo 2. 无头模式: python kdocs_auto_sync.py --headless
echo 3. 指定内容: python kdocs_auto_sync.py --content "要同步的内容"
echo.
echo 首次使用时需要在浏览器中登录金山文档账号
echo.
pause
