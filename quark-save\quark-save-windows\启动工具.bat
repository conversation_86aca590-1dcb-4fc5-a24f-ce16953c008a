@echo off
setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    pause
    exit /b 1
)

echo ========================================
echo         Quark Cloud Disk Tool v3.0
echo ========================================
echo.
echo 1. Start Web Interface (Recommended)
echo 2. Start Background Service
echo 3. Command Line Mode
echo 4. Exit
echo.
set /p choice=Please select option (1-4): 

if "%choice%"=="1" goto web_mode
if "%choice%"=="2" goto background_mode
if "%choice%"=="3" goto cmd_mode
if "%choice%"=="4" goto exit_script
echo Invalid choice, using web mode...

:web_mode
echo.
echo Starting Web Interface...
echo URL: http://127.0.0.1:8080
echo.
echo Features available in web interface:
echo - All basic functions (sign, save, share, sync)
echo - API functions (today, list, search)
echo - System management (service, tasks, logs, files)
echo - Real-time status monitoring
echo.
echo Press Ctrl+C to stop the server
echo.
start "" "http://127.0.0.1:8080"
"%PHP_PATH%" -S 127.0.0.1:8080 server.php
goto exit_script

:background_mode
echo.
echo Starting Background Service...
netstat -ano | find ":8080" >nul 2>nul
if %errorlevel% equ 0 (
    echo Service is already running
    start "" "http://127.0.0.1:8080"
    pause
    goto exit_script
)
if not exist "runtime" mkdir runtime
start /B "" "%PHP_PATH%" -S 127.0.0.1:8080 server.php >runtime\service.log 2>nul
timeout /t 2 /nobreak >nul
netstat -ano | find ":8080" >nul 2>nul
if %errorlevel% equ 0 (
    echo Background service started!
    echo Access: http://127.0.0.1:8080
    start "" "http://127.0.0.1:8080"
) else (
    echo Failed to start service.
)
pause
goto exit_script

:cmd_mode
echo.
echo Command Line Mode
echo Available commands:
echo - php\php.exe QuarkService.php --options sign
echo - php\php.exe QuarkService.php --options api_today
echo - php\php.exe QuarkService.php --options api_list
echo - php\php.exe QuarkService.php --options api_search --search "keyword"
echo.
echo Type 'exit' to return
cmd /k "cd /d %SCRIPT_DIR%"
goto exit_script

:exit_script
echo.
echo Thank you for using Quark Tool!
exit /b 0
