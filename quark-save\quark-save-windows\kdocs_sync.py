#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金山文档自动同步脚本
支持简化同步和完整自动化同步两种模式
"""

import os
import sys
import time
import webbrowser
import subprocess
import argparse
from datetime import datetime

def install_package(package_name):
    """自动安装Python包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name],
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print(f"✅ {package_name} 安装成功")
        return True
    except Exception as e:
        print(f"❌ 无法安装 {package_name}: {e}")
        return False

# 自动安装基础依赖
try:
    import pyperclip
except ImportError:
    if not install_package("pyperclip"):
        sys.exit(1)
    import pyperclip

# 尝试导入Selenium（可选）
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.keys import Keys
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class KdocsAutoSync:
    def __init__(self, mode='simple'):
        self.doc_url = "https://kdocs.cn/l/cqLsUjOoGFoV"
        self.mode = mode  # 'simple' 或 'full'
        self.driver = None
        self.wait = None
    
    def get_latest_backup_content(self):
        """获取最新的备份文件内容"""
        try:
            backup_dir = os.path.join(os.path.dirname(__file__), 'kdocs_backup')
            if not os.path.exists(backup_dir):
                print("❌ 备份目录不存在")
                return None
            
            # 查找最新的.md文件
            md_files = [f for f in os.listdir(backup_dir) if f.endswith('.md')]
            if not md_files:
                print("❌ 没有找到备份文件")
                return None
            
            # 按修改时间排序，获取最新的
            latest_file = max(md_files, key=lambda f: os.path.getmtime(os.path.join(backup_dir, f)))
            latest_path = os.path.join(backup_dir, latest_file)
            
            with open(latest_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 读取最新备份文件: {latest_file}")
            return content
            
        except Exception as e:
            print(f"❌ 读取备份文件失败: {e}")
            return None
    
    def copy_to_clipboard(self, content):
        """复制内容到剪贴板"""
        try:
            pyperclip.copy(content)
            print("📋 内容已复制到剪贴板")
            return True
        except Exception as e:
            print(f"❌ 复制到剪贴板失败: {e}")
            return False
    
    def open_document(self):
        """打开金山文档"""
        try:
            print(f"🌐 正在打开金山文档: {self.doc_url}")
            webbrowser.open(self.doc_url)
            return True
        except Exception as e:
            print(f"❌ 打开文档失败: {e}")
            return False
    
    def show_instructions(self):
        """显示操作说明"""
        print("\n" + "="*50)
        print("📝 手动粘贴说明")
        print("="*50)
        print("1. 内容已复制到剪贴板")
        print("2. 金山文档将在浏览器中打开")
        print("3. 请在文档中找到合适位置")
        print("4. 按 Ctrl+V 粘贴内容")
        print("5. 保存文档")
        print("="*50)
    
    def setup_selenium(self, headless=True):
        """设置Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            return False

        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')

            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

            # 设置用户数据目录以保持登录状态
            user_data_dir = os.path.join(os.path.dirname(__file__), 'chrome_user_data')
            chrome_options.add_argument(f'--user-data-dir={user_data_dir}')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 30)

            print("✅ Selenium WebDriver 初始化成功")
            return True

        except Exception as e:
            print(f"❌ Selenium WebDriver 初始化失败: {e}")
            return False

    def full_auto_sync(self, content):
        """完整自动化同步"""
        try:
            print("🤖 开始完整自动化同步...")

            if not self.setup_selenium():
                return False

            # 打开金山文档
            print(f"🌐 正在打开金山文档: {self.doc_url}")
            self.driver.get(self.doc_url)
            time.sleep(3)

            # 检查是否需要登录
            if "login" in self.driver.current_url.lower():
                print("🔐 需要登录，请在浏览器中完成登录...")
                input("登录完成后按回车键继续...")
                self.driver.get(self.doc_url)
                time.sleep(3)

            # 查找编辑器
            editor_selectors = [
                "div[contenteditable='true']",
                ".ql-editor",
                ".editor-content",
                "textarea"
            ]

            editor = None
            for selector in editor_selectors:
                try:
                    editor = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except TimeoutException:
                    continue

            if not editor:
                print("❌ 未找到文档编辑器")
                return False

            # 滚动到文档底部并插入内容
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)

            editor.click()
            time.sleep(1)
            editor.send_keys(Keys.CONTROL + Keys.END)
            time.sleep(1)
            editor.send_keys(Keys.ENTER)
            editor.send_keys(content)

            # 保存
            try:
                save_button = self.driver.find_element(By.CSS_SELECTOR, "button[title*='保存']")
                save_button.click()
            except:
                editor.send_keys(Keys.CONTROL + "s")

            time.sleep(2)
            print("✅ 完整自动化同步完成")
            return True

        except Exception as e:
            print(f"❌ 完整自动化同步失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

    def auto_sync(self, content=None):
        """执行自动同步"""
        try:
            # 获取内容
            if content:
                sync_content = content
            else:
                sync_content = self.get_latest_backup_content()
                if not sync_content:
                    return False

            # 根据模式选择同步方式
            if self.mode == 'full' and SELENIUM_AVAILABLE:
                print("🤖 使用完整自动化模式...")
                return self.full_auto_sync(sync_content)
            else:
                print("🚀 使用简化自动同步模式...")

                # 复制到剪贴板
                if not self.copy_to_clipboard(sync_content):
                    return False

                # 打开文档
                if not self.open_document():
                    return False

                # 显示说明
                self.show_instructions()

                print("✅ 简化自动同步完成！")
                print("💡 请在浏览器中手动粘贴内容")

                return True

        except Exception as e:
            print(f"❌ 自动同步失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='金山文档自动同步工具')
    parser.add_argument('--content', type=str, help='直接指定要同步的内容')
    parser.add_argument('--mode', type=str, choices=['simple', 'full'], default='simple',
                       help='同步模式：simple=简化模式，full=完整自动化模式')
    parser.add_argument('--install-selenium', action='store_true',
                       help='安装Selenium依赖（用于完整自动化模式）')

    args = parser.parse_args()

    # 如果用户要求安装Selenium
    if args.install_selenium:
        print("📦 正在安装Selenium依赖...")
        packages = ['selenium', 'webdriver-manager']
        for package in packages:
            if not install_package(package):
                print(f"❌ 安装 {package} 失败")
                sys.exit(1)
        print("✅ Selenium依赖安装完成")
        return

    # 检查模式可用性
    if args.mode == 'full' and not SELENIUM_AVAILABLE:
        print("⚠️  完整自动化模式需要Selenium依赖")
        print("请运行: python kdocs_sync.py --install-selenium")
        print("🔄 自动切换到简化模式...")
        args.mode = 'simple'

    sync_tool = KdocsAutoSync(mode=args.mode)

    try:
        success = sync_tool.auto_sync(args.content)
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
