@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"

echo ========================================
echo         Quick Task Setup
echo ========================================
echo.
echo This will create a daily update task (9:00 AM).
echo.

net session >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Administrator privileges required!
    echo Please run as administrator.
    pause
    exit /b 1
)

schtasks /query /tn "QuarkDailyUpdate" >nul 2>&1
if %errorlevel% equ 0 (
    echo Updating existing task...
    schtasks /delete /tn "QuarkDailyUpdate" /f >nul 2>&1
)

echo Creating daily update task...
schtasks /create /tn "QuarkDailyUpdate" /tr "\"%SCRIPT_DIR%每日自动更新.bat\" auto" /sc daily /st 09:00 /ru "SYSTEM" /f >nul 2>&1

if %errorlevel% equ 0 (
    echo Task created successfully!
    echo Schedule: Daily at 9:00 AM
    echo.
    echo To manage tasks, use Windows Task Scheduler (taskschd.msc)
) else (
    echo Failed to create task.
    echo Please check the error messages above.
)

pause
