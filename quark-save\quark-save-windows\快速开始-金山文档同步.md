# 🚀 金山文档同步 - 快速开始指南

## 📋 概述

本指南将帮助您快速设置和使用金山文档自动同步功能，解决手动复制粘贴的问题。

## ⚡ 5分钟快速设置

### 第一步：安装自动同步依赖

1. **双击运行安装脚本**：
   ```
   install_auto_sync.bat
   ```

2. **按照提示操作**：
   - 确认Python环境
   - 安装基础依赖（pyperclip）
   - 选择是否安装完整自动化依赖

3. **安装完成**：
   - 看到"安装完成"提示即可

### 第二步：启动Web服务

1. **启动服务器**：
   ```bash
   php\php.exe server.php
   ```

2. **打开Web界面**：
   - 浏览器访问：http://127.0.0.1:8080

### 第三步：测试同步功能

1. **进入管理页面**：
   - 点击导航栏"管理"按钮
   - 选择"金山文档"选项卡

2. **测试自动同步**：
   - 点击"自动同步（推荐）"按钮
   - 首次使用会提示登录金山文档
   - 按照提示完成操作

## 🎯 使用场景

### 场景1：每日分享后自动同步
```bash
# 执行分享操作，自动触发同步
php\php.exe QuarkService.php --options share
```

### 场景2：手动触发同步
1. 打开Web界面
2. 点击"自动同步"按钮
3. 等待完成或按提示操作

### 场景3：查看和管理备份
1. 在"金山文档"选项卡中查看备份列表
2. 点击绿色按钮快速复制并打开文档
3. 点击眼睛图标查看内容详情

## 🔧 常见问题

### Q: 自动同步失败怎么办？
**A:** 
1. 检查Python环境是否正确安装
2. 重新运行 `install_auto_sync.bat`
3. 使用手动同步作为备选方案

### Q: 如何知道同步是否成功？
**A:**
1. 查看Web界面的日志输出
2. 检查金山文档是否有新内容
3. 查看备份文件列表

### Q: 可以修改目标文档吗？
**A:**
1. 编辑 `KdocsSync.php` 文件
2. 修改 `$this->docUrl` 的值
3. 重启服务器

## 💡 使用技巧

### 技巧1：选择最适合的同步方式
- **简化自动同步**：适合大多数用户，只需手动粘贴
- **完整自动化**：适合技术用户，完全自动化
- **手动同步**：兼容性最好，适合所有环境

### 技巧2：备份文件管理
- 定期清理旧的备份文件
- 重要内容可以手动备份
- 使用备份文件恢复误删内容

### 技巧3：提高同步效率
- 保持浏览器登录状态
- 使用快捷键 Ctrl+V 粘贴
- 定期检查同步结果

## 🆘 获取帮助

### 查看详细文档
- 阅读 `金山文档同步说明.md`
- 查看故障排除部分

### 检查日志
- Web界面日志区域
- `logs/system/kdocs_sync_*.log` 文件

### 常用命令
```bash
# 查看Python版本
python --version

# 手动安装依赖
pip install pyperclip

# 测试简化同步
python kdocs_simple_sync.py

# 测试完整自动化
python kdocs_auto_sync.py
```

## 🎉 开始使用

现在您已经完成了设置，可以开始享受自动同步的便利了！

1. **日常使用**：执行分享操作后自动同步
2. **手动同步**：随时通过Web界面触发同步
3. **备份管理**：查看和管理所有同步记录

---

**祝您使用愉快！** 🚀

如有问题，请查看详细文档或检查日志信息。
