@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

echo ========================================
echo         Log Cleanup Tool
echo ========================================
echo.

if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    pause
    exit /b 1
)

if exist "logs" (
    echo Current log statistics:
    "%PHP_PATH%" -r "include 'LogManager.php'; $lm = new LogManager(); $stats = $lm->getLogStats(); foreach($stats as $type => $stat) { echo \"$type: {$stat['count']} files, {$stat['size']}\n\"; }" 2>nul
    echo.

    set /p days=Enter days to keep (default 30):
    if "!days!"=="" set days=30

    echo.
    set /p confirm=Delete logs older than !days! days? (Y/N):

    if /i "!confirm!"=="Y" (
        echo Cleaning logs...
        "%PHP_PATH%" -r "include 'LogManager.php'; $lm = new LogManager(); $lm->cleanOldLogs(!days!); echo 'Cleanup completed.\n';" 2>nul
    ) else (
        echo Cleanup cancelled.
    )
) else (
    echo No logs directory found.
)

pause
