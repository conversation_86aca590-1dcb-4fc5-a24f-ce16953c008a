@echo off
setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    pause
    exit /b 1
)

:main_menu
cls
echo ========================================
echo         Quark Cloud Disk Tool v3.0
echo ========================================
echo.
echo 1. Start Web Service (Interactive)
echo 2. Start Background Service
echo 3. Stop Background Service
echo 4. Check Service Status
echo 5. Command Line Mode
echo 6. Daily Update
echo 7. Clean Logs
echo 8. Exit
echo.
set /p choice=Please select option (1-8): 

if "%choice%"=="1" goto web_interactive
if "%choice%"=="2" goto web_background
if "%choice%"=="3" goto stop_service
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto cmd_mode
if "%choice%"=="6" goto daily_update
if "%choice%"=="7" goto clean_logs
if "%choice%"=="8" goto exit_script
echo Invalid choice, please try again.
pause
goto main_menu

:web_interactive
echo.
echo Starting Web Service (Interactive Mode)...
echo Service URL: http://127.0.0.1:8080
echo Press Ctrl+C to stop the server
echo.
start "" "http://127.0.0.1:8080"
"%PHP_PATH%" -S 127.0.0.1:8080 server.php
echo.
echo Server stopped.
pause
goto main_menu

:web_background
echo.
echo Starting Background Service...
netstat -ano | find ":8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo Service is already running on port 8080
    start "" "http://127.0.0.1:8080"
    pause
    goto main_menu
)
if not exist "runtime" mkdir runtime
start /B "" "%PHP_PATH%" -S 127.0.0.1:8080 server.php >runtime\service.log 2>&1
timeout /t 2 /nobreak >nul
netstat -ano | find ":8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo Background service started successfully!
    echo Access: http://127.0.0.1:8080
    start "" "http://127.0.0.1:8080"
) else (
    echo Failed to start background service.
)
pause
goto main_menu

:stop_service
echo.
echo Stopping Background Service...
for /f "tokens=5" %%a in ('netstat -ano ^| find ":8080" ^| find "LISTENING"') do (
    echo Stopping process PID: %%a
    taskkill /PID %%a /F >nul 2>&1
)
echo Background service stopped.
pause
goto main_menu

:check_status
echo.
echo ========================================
echo         Service Status
echo ========================================
echo.
netstat -ano | find ":8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo Status: RUNNING
    echo Access: http://127.0.0.1:8080
) else (
    echo Status: NOT RUNNING
)
echo.
pause
goto main_menu

:cmd_mode
echo.
echo Entering Command Line Mode...
echo Available commands: sign, save, share, syn_dir, auto, api_today, api_list, api_search
echo Example: php\php.exe QuarkService.php --options sign
echo Type 'exit' to return to menu
echo.
cmd /k "cd /d %SCRIPT_DIR%"
goto main_menu

:daily_update
echo.
echo Starting Daily Update...
"%PHP_PATH%" QuarkService.php --options api_auto_update
echo.
echo Update completed.
pause
goto main_menu

:clean_logs
echo.
echo Log Cleanup...
if exist "logs" (
    echo Cleaning logs older than 30 days...
    "%PHP_PATH%" -r "include 'LogManager.php'; $lm = new LogManager(); $lm->cleanOldLogs(30); echo 'Cleanup completed.\n';" 2>nul
) else (
    echo No logs directory found.
)
pause
goto main_menu

:exit_script
echo.
echo Thank you for using Quark Cloud Disk Tool!
exit /b 0
