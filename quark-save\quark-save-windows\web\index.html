<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>夸克网盘自动化工具</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>☁️</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-cloud-arrow-down"></i>
                    夸克网盘自动化工具
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/config">
                        <i class="bi bi-gear"></i> 设置
                    </a>
                    <a class="nav-link" href="#" onclick="showManageModal()">
                        <i class="bi bi-tools"></i> 管理
                    </a>
                </div>
            </div>
        </nav>

        <div class="container">
            <!-- 状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card status-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="card-title mb-1">系统状态</h5>
                                    <p class="card-text text-muted mb-0" id="status-text">正在检查系统状态...</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="badge bg-secondary" id="status-badge">检查中</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能按钮区域 -->
            <div class="row">
                <!-- 基础功能 -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="bi bi-check-circle"></i> 基础功能</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-success btn-lg" onclick="executeAction('sign')">
                                    <i class="bi bi-calendar-check"></i> 自动签到
                                </button>
                                <button class="btn btn-outline-primary btn-lg" onclick="showSaveDialog()">
                                    <i class="bi bi-download"></i> 转存资源
                                </button>
                                <button class="btn btn-outline-info btn-lg" onclick="executeAction('share')">
                                    <i class="bi bi-share"></i> 分享资源
                                </button>
                                <button class="btn btn-outline-warning btn-lg" onclick="executeAction('syn_dir')">
                                    <i class="bi bi-folder-symlink"></i> 同步目录
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级功能 -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-stars"></i> 高级功能</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-info btn-lg" onclick="executeAction('auto', {update: 'all'})">
                                    <i class="bi bi-arrow-clockwise"></i> 更新全部短剧
                                </button>
                                <button class="btn btn-outline-info btn-lg" onclick="executeAction('auto', {update: 'daily'})">
                                    <i class="bi bi-calendar-plus"></i> 更新每日短剧
                                </button>
                                <button class="btn btn-outline-secondary btn-lg" onclick="showSearchDialog()">
                                    <i class="bi bi-search"></i> 搜索短剧
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API功能区域 -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-gradient" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4);">
                            <h5 class="mb-0 text-white"><i class="bi bi-cloud-download"></i> 短剧API功能 <span class="badge bg-light text-dark">NEW</span></h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-success btn-lg w-100" onclick="executeApiAction('api_today')">
                                        <i class="bi bi-calendar-check"></i> 今日更新
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-primary btn-lg w-100" onclick="executeApiAction('api_list')">
                                        <i class="bi bi-list-ul"></i> 全部短剧
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-warning btn-lg w-100" onclick="showApiSearchDialog()">
                                        <i class="bi bi-search-heart"></i> API搜索
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-danger btn-lg w-100" onclick="executeApiAction('api_auto_update')">
                                        <i class="bi bi-lightning"></i> 自动更新
                                    </button>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="alert alert-info mb-0">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>API功能说明：</strong>
                                    使用最新的短剧API接口，支持今日更新、全部列表、关键词搜索和自动转存功能。
                                    数据来源：<code>duanju.click</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作日志 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-terminal"></i> 操作日志</h5>
                        </div>
                        <div class="card-body">
                            <div id="log-container" class="log-container">
                                <p class="text-muted">等待操作...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 转存资源对话框 -->
    <div class="modal fade" id="saveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">转存资源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">选择资源文件</label>
                        <input type="file" class="form-control" id="fileInput" accept=".txt,.csv,.xlsx,.xls">
                        <div class="form-text">支持 txt、csv、xlsx、xls 格式</div>
                    </div>
                    <div class="mb-3">
                        <label for="filePathInput" class="form-label">或输入文件路径</label>
                        <input type="text" class="form-control" id="filePathInput" placeholder="例如: resources.txt">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeSave()">开始转存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索短剧对话框 -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">搜索短剧</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="dramaNameInput" class="form-label">短剧名称</label>
                        <input type="text" class="form-control" id="dramaNameInput" placeholder="请输入要搜索的短剧名称">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeSearch()">开始搜索</button>
                </div>
            </div>
        </div>
    </div>

    <!-- API搜索对话框 -->
    <div class="modal fade" id="apiSearchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-gradient" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4);">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-search-heart"></i> API搜索短剧
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="apiSearchInput" class="form-label">短剧名称</label>
                        <input type="text" class="form-control" id="apiSearchInput" placeholder="请输入要搜索的短剧名称">
                        <div class="form-text">使用API接口搜索，数据更全面准确</div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="apiAutoSave" checked>
                        <label class="form-check-label" for="apiAutoSave">
                            搜索后自动转存到网盘
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeApiSearch()">
                        <i class="bi bi-search"></i> 开始搜索
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统管理对话框 -->
    <div class="modal fade" id="manageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-dark text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-tools"></i> 系统管理
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 管理选项卡 -->
                    <ul class="nav nav-tabs" id="manageTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="service-tab" data-bs-toggle="tab" data-bs-target="#service" type="button" role="tab">
                                <i class="bi bi-server"></i> 服务管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab">
                                <i class="bi bi-calendar-check"></i> 定时任务
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                                <i class="bi bi-file-text"></i> 日志管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab">
                                <i class="bi bi-folder"></i> 文件管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="kdocs-tab" data-bs-toggle="tab" data-bs-target="#kdocs" type="button" role="tab">
                                <i class="bi bi-cloud-upload"></i> 金山文档
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="manageTabContent">
                        <!-- 服务管理 -->
                        <div class="tab-pane fade show active" id="service" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">服务控制</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-success" onclick="serviceAction('start')">
                                                    <i class="bi bi-play-circle"></i> 启动服务
                                                </button>
                                                <button class="btn btn-danger" onclick="serviceAction('stop')">
                                                    <i class="bi bi-stop-circle"></i> 停止服务
                                                </button>
                                                <button class="btn btn-warning" onclick="serviceAction('restart')">
                                                    <i class="bi bi-arrow-clockwise"></i> 重启服务
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">服务状态</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="service-status">
                                                <p class="text-muted">正在检查服务状态...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 定时任务 -->
                        <div class="tab-pane fade" id="tasks" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">任务操作</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-primary" onclick="taskAction('create_daily')">
                                                    <i class="bi bi-plus-circle"></i> 创建每日更新任务
                                                </button>
                                                <button class="btn btn-danger" onclick="taskAction('delete_tasks')">
                                                    <i class="bi bi-trash"></i> 删除所有任务
                                                </button>
                                                <button class="btn btn-info" onclick="loadTasks()">
                                                    <i class="bi bi-arrow-clockwise"></i> 刷新任务列表
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="openTaskScheduler()">
                                                    <i class="bi bi-gear"></i> 打开任务计划程序
                                                </button>
                                            </div>
                                            <div class="mt-3">
                                                <div class="alert alert-warning">
                                                    <small>
                                                        <i class="bi bi-exclamation-triangle"></i>
                                                        <strong>权限要求：</strong>创建定时任务需要管理员权限<br>
                                                        <strong>解决方案：</strong><br>
                                                        1. 以管理员身份运行浏览器<br>
                                                        2. 或右键运行"创建定时任务.bat"<br>
                                                        3. 或点击"打开任务计划程序"手动创建
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">当前任务</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="tasks-list">
                                                <p class="text-muted">正在加载任务列表...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 日志管理 -->
                        <div class="tab-pane fade" id="logs" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">日志操作</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="cleanDays" class="form-label">保留天数</label>
                                                <input type="number" class="form-control" id="cleanDays" value="30" min="1" max="365">
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-warning" onclick="cleanLogs()">
                                                    <i class="bi bi-trash"></i> 清理旧日志
                                                </button>
                                                <button class="btn btn-info" onclick="loadLogs()">
                                                    <i class="bi bi-arrow-clockwise"></i> 刷新日志列表
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">最近日志文件</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="logs-list" style="max-height: 300px; overflow-y: auto;">
                                                <p class="text-muted">正在加载日志列表...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 文件管理 -->
                        <div class="tab-pane fade" id="files" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">文件上传</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="uploadFile" class="form-label">选择文件</label>
                                                <input type="file" class="form-control" id="uploadFile" accept=".txt,.csv,.xlsx,.xls">
                                            </div>
                                            <button class="btn btn-primary w-100" onclick="uploadFile()">
                                                <i class="bi bi-upload"></i> 上传文件
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">资源文件</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="files-list" style="max-height: 300px; overflow-y: auto;">
                                                <p class="text-muted">正在加载文件列表...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 金山文档同步 -->
                        <div class="tab-pane fade" id="kdocs" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-cloud-upload"></i> 同步操作
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">目标文档</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="kdocsUrl" value="https://kdocs.cn/l/cqLsUjOoGFoV" readonly>
                                                    <button class="btn btn-outline-secondary" onclick="openKdocs()" title="打开金山文档">
                                                        <i class="bi bi-box-arrow-up-right"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">火星生活科技短剧库</div>
                                            </div>

                                            <!-- 同步状态指示器 -->
                                            <div id="sync-status" class="mb-3" style="display: none;">
                                                <div class="alert alert-warning mb-0">
                                                    <div class="d-flex align-items-center">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                        <span>正在生成同步内容...</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-grid gap-2">
                                                <button class="btn btn-success" id="auto-sync-btn" onclick="autoSyncToKdocs()">
                                                    <i class="bi bi-robot"></i> 自动同步（推荐）
                                                </button>
                                                <button class="btn btn-primary" id="sync-btn" onclick="syncToKdocs()">
                                                    <i class="bi bi-cloud-upload"></i> 生成同步内容
                                                </button>
                                                <button class="btn btn-outline-success" id="copy-and-open-btn" onclick="copyLatestAndOpen()" style="display: none;">
                                                    <i class="bi bi-clipboard-check"></i> 复制内容并打开文档
                                                </button>
                                                <button class="btn btn-info" onclick="loadKdocsBackups()">
                                                    <i class="bi bi-arrow-clockwise"></i> 刷新备份列表
                                                </button>
                                            </div>

                                            <div class="mt-3">
                                                <div class="alert alert-success mb-0">
                                                    <small>
                                                        <i class="bi bi-robot"></i>
                                                        <strong>自动同步（推荐）：</strong><br>
                                                        1. 点击"自动同步"按钮<br>
                                                        2. 内容自动复制到剪贴板，文档自动打开<br>
                                                        3. 在文档中按 Ctrl+V 粘贴即可
                                                    </small>
                                                </div>
                                                <div class="alert alert-info mb-0 mt-2">
                                                    <small>
                                                        <i class="bi bi-info-circle"></i>
                                                        <strong>手动同步：</strong><br>
                                                        1. 点击"生成同步内容"<br>
                                                        2. 点击"复制内容并打开文档"<br>
                                                        3. 在文档中粘贴内容（Ctrl+V）
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">同步备份文件</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="kdocs-backups" style="max-height: 300px; overflow-y: auto;">
                                                <p class="text-muted">正在加载备份文件...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 内容查看对话框 -->
    <div class="modal fade" id="contentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-file-text"></i> 同步内容预览
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">文件名</label>
                        <input type="text" class="form-control" id="contentFilename" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">内容 <button class="btn btn-sm btn-outline-primary" onclick="copyContent()">复制内容</button></label>
                        <textarea class="form-control" id="contentText" rows="15" readonly style="font-family: monospace;"></textarea>
                    </div>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        <strong>快速同步步骤：</strong><br>
                        1. 点击"复制并打开文档"按钮<br>
                        2. 在自动打开的文档中粘贴内容（Ctrl+V）<br>
                        3. 保存文档
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="copyContentAndOpen()">
                        <i class="bi bi-clipboard-check"></i> 复制并打开文档
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="copyContent()">
                        <i class="bi bi-clipboard"></i> 仅复制内容
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
