<?php
// Web服务器路由处理
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($method === 'OPTIONS') {
    exit(0);
}

// 路由处理
switch ($uri) {
    case '/':
    case '/index.html':
        include 'web/index.html';
        break;
        
    case '/config':
    case '/config.html':
        include 'web/config.html';
        break;
        
    case '/api/status':
        handleApiStatus();
        break;
        
    case '/api/config':
        handleApiConfig();
        break;
        
    case '/api/execute':
        handleApiExecute();
        break;
        
    case '/api/logs':
        handleApiLogs();
        break;

    case '/api/service':
        handleApiService();
        break;

    case '/api/tasks':
        handleApiTasks();
        break;

    case '/api/files':
        handleApiFiles();
        break;

    case '/api/kdocs':
        handleApiKdocs();
        break;

    default:
        // 处理静态文件
        $file = __DIR__ . '/web' . $uri;
        if (file_exists($file) && is_file($file)) {
            $ext = pathinfo($file, PATHINFO_EXTENSION);
            $mimeTypes = [
                'css' => 'text/css',
                'js' => 'application/javascript',
                'png' => 'image/png',
                'jpg' => 'image/jpeg',
                'gif' => 'image/gif',
                'ico' => 'image/x-icon'
            ];
            
            if (isset($mimeTypes[$ext])) {
                header('Content-Type: ' . $mimeTypes[$ext]);
            }
            
            readfile($file);
        } else {
            http_response_code(404);
            echo '404 Not Found';
        }
        break;
}

function handleApiStatus() {
    header('Content-Type: application/json');
    
    $cookieFile = __DIR__ . '/cookie.txt';
    $hasCookie = false;
    
    if (file_exists($cookieFile)) {
        $content = file_get_contents($cookieFile);
        $lines = explode("\n", $content);
        
        foreach($lines as $line) {
            $line = trim($line);
            if(!empty($line) && strpos($line, '#') !== 0) {
                $hasCookie = true;
                break;
            }
        }
    }
    
    echo json_encode([
        'status' => 'ok',
        'hasCookie' => $hasCookie,
        'phpVersion' => PHP_VERSION,
        'timestamp' => time()
    ]);
}

function handleApiConfig() {
    header('Content-Type: application/json');
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (isset($input['cookie'])) {
            $cookieFile = __DIR__ . '/cookie.txt';
            file_put_contents($cookieFile, $input['cookie']);
            
            echo json_encode(['status' => 'success', 'message' => 'Cookie保存成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '无效的Cookie数据']);
        }
    } else {
        // 获取当前配置
        $cookieFile = __DIR__ . '/cookie.txt';
        $cookie = '';
        
        if (file_exists($cookieFile)) {
            $content = file_get_contents($cookieFile);
            $lines = explode("\n", $content);
            
            foreach($lines as $line) {
                $line = trim($line);
                if(!empty($line) && strpos($line, '#') !== 0) {
                    $cookie = $line;
                    break;
                }
            }
        }
        
        echo json_encode([
            'status' => 'success',
            'cookie' => $cookie
        ]);
    }
}

function handleApiExecute() {
    header('Content-Type: application/json');
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['status' => 'error', 'message' => '仅支持POST请求']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    $params = $input['params'] ?? [];
    
    $phpPath = __DIR__ . '/php/php.exe';
    $scriptPath = __DIR__ . '/QuarkService.php';
    
    $command = '"' . $phpPath . '" "' . $scriptPath . '" --options ' . escapeshellarg($action);

    // 添加额外参数
    foreach ($params as $key => $value) {
        if ($key === 'auto-save') {
            // auto-save 参数特殊处理
            $command .= ' --auto-save ' . escapeshellarg($value);
        } else {
            $command .= ' --' . escapeshellarg($key) . ' ' . escapeshellarg($value);
        }
    }
    
    // 执行命令并捕获输出
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    echo json_encode([
        'status' => $returnCode === 0 ? 'success' : 'error',
        'output' => implode("\n", $output),
        'command' => $command
    ]);
}

function handleApiLogs() {
    header('Content-Type: application/json');

    $logsDir = __DIR__ . '/logs';
    $logs = [];

    if (is_dir($logsDir)) {
        $types = ['api', 'system', 'share', 'sync'];
        foreach ($types as $type) {
            $typeDir = $logsDir . '/' . $type;
            if (is_dir($typeDir)) {
                $files = glob($typeDir . '/*.txt');
                foreach ($files as $file) {
                    $logs[] = [
                        'type' => $type,
                        'name' => basename($file),
                        'size' => filesize($file),
                        'modified' => filemtime($file),
                        'path' => str_replace(__DIR__, '', $file)
                    ];
                }
            }
        }
    }

    // 按修改时间排序
    usort($logs, function($a, $b) {
        return $b['modified'] - $a['modified'];
    });

    echo json_encode([
        'status' => 'success',
        'logs' => array_slice($logs, 0, 20) // 只返回最新20个
    ]);
}

function handleApiService() {
    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';

        switch ($action) {
            case 'start':
                // 检查是否已经运行
                $output = shell_exec('netstat -ano | find ":8080"');
                if (!empty($output)) {
                    echo json_encode(['status' => 'error', 'message' => '服务已在运行']);
                    return;
                }

                // 启动后台服务
                $phpPath = __DIR__ . '/php/php.exe';
                $command = 'start /B "" "' . $phpPath . '" -S 127.0.0.1:8080 server.php';
                pclose(popen($command, 'r'));

                echo json_encode(['status' => 'success', 'message' => '后台服务已启动']);
                break;

            case 'stop':
                // 停止服务
                $output = shell_exec('netstat -ano | find ":8080"');
                if (preg_match('/\s+(\d+)$/', trim($output), $matches)) {
                    $pid = $matches[1];
                    shell_exec("taskkill /PID $pid /F");
                    echo json_encode(['status' => 'success', 'message' => '服务已停止']);
                } else {
                    echo json_encode(['status' => 'error', 'message' => '未找到运行的服务']);
                }
                break;

            case 'restart':
                // 重启服务
                $output = shell_exec('netstat -ano | find ":8080"');
                if (preg_match('/\s+(\d+)$/', trim($output), $matches)) {
                    $pid = $matches[1];
                    shell_exec("taskkill /PID $pid /F");
                    sleep(2);
                }

                $phpPath = __DIR__ . '/php/php.exe';
                $command = 'start /B "" "' . $phpPath . '" -S 127.0.0.1:8080 server.php';
                pclose(popen($command, 'r'));

                echo json_encode(['status' => 'success', 'message' => '服务已重启']);
                break;

            default:
                echo json_encode(['status' => 'error', 'message' => '未知操作']);
        }
    } else {
        // 获取服务状态
        $output = shell_exec('netstat -ano | find ":8080"');
        $isRunning = !empty($output);

        echo json_encode([
            'status' => 'success',
            'isRunning' => $isRunning,
            'port' => 8080
        ]);
    }
}

function handleApiTasks() {
    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';

        switch ($action) {
            case 'create_daily':
                $command = 'schtasks /create /tn "QuarkDailyUpdate" /tr "\"' . __DIR__ . '\\每日自动更新.bat\" auto" /sc daily /st 09:00 /ru "SYSTEM" /f';
                exec($command, $output, $returnCode);

                echo json_encode([
                    'status' => $returnCode === 0 ? 'success' : 'error',
                    'message' => $returnCode === 0 ? '每日更新任务创建成功' : '任务创建失败'
                ]);
                break;

            case 'delete_tasks':
                $tasks = ['QuarkDailyUpdate', 'QuarkAutoStart', 'QuarkLogCleanup'];
                $deleted = 0;

                foreach ($tasks as $task) {
                    exec("schtasks /delete /tn \"$task\" /f", $output, $returnCode);
                    if ($returnCode === 0) $deleted++;
                }

                echo json_encode([
                    'status' => 'success',
                    'message' => "已删除 $deleted 个任务"
                ]);
                break;

            case 'list':
                exec('schtasks /query /fo csv | find "Quark"', $output);
                $tasks = [];

                foreach ($output as $line) {
                    if (strpos($line, 'Quark') !== false) {
                        $parts = str_getcsv($line);
                        if (count($parts) >= 2) {
                            $tasks[] = [
                                'name' => $parts[0],
                                'status' => $parts[1] ?? 'Unknown'
                            ];
                        }
                    }
                }

                echo json_encode([
                    'status' => 'success',
                    'tasks' => $tasks
                ]);
                break;

            default:
                echo json_encode(['status' => 'error', 'message' => '未知操作']);
        }
    } else {
        // 获取任务列表
        exec('schtasks /query /fo csv | find "Quark"', $output);
        $tasks = [];

        foreach ($output as $line) {
            if (strpos($line, 'Quark') !== false) {
                $parts = str_getcsv($line);
                if (count($parts) >= 2) {
                    $tasks[] = [
                        'name' => $parts[0],
                        'status' => $parts[1] ?? 'Unknown'
                    ];
                }
            }
        }

        echo json_encode([
            'status' => 'success',
            'tasks' => $tasks
        ]);
    }
}

function handleApiFiles() {
    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';

        switch ($action) {
            case 'clean_logs':
                $days = intval($input['days'] ?? 30);

                if (class_exists('LogManager')) {
                    include_once 'LogManager.php';
                    $lm = new LogManager();
                    $result = $lm->cleanOldLogs($days);

                    echo json_encode([
                        'status' => 'success',
                        'message' => "已清理超过 $days 天的日志文件"
                    ]);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'LogManager类不存在']);
                }
                break;

            case 'upload':
                if (isset($_FILES['file'])) {
                    $uploadDir = __DIR__ . '/uploads/';
                    if (!is_dir($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    $fileName = basename($_FILES['file']['name']);
                    $targetPath = $uploadDir . $fileName;

                    if (move_uploaded_file($_FILES['file']['tmp_name'], $targetPath)) {
                        echo json_encode([
                            'status' => 'success',
                            'message' => '文件上传成功',
                            'path' => $targetPath
                        ]);
                    } else {
                        echo json_encode(['status' => 'error', 'message' => '文件上传失败']);
                    }
                } else {
                    echo json_encode(['status' => 'error', 'message' => '未找到上传文件']);
                }
                break;

            default:
                echo json_encode(['status' => 'error', 'message' => '未知操作']);
        }
    } else {
        // 获取文件列表
        $files = [];
        $patterns = ['*.txt', '*.csv', '*.xlsx', '*.xls'];

        foreach ($patterns as $pattern) {
            $matches = glob(__DIR__ . '/' . $pattern);
            foreach ($matches as $file) {
                if (is_file($file)) {
                    $files[] = [
                        'name' => basename($file),
                        'size' => filesize($file),
                        'modified' => filemtime($file),
                        'type' => pathinfo($file, PATHINFO_EXTENSION)
                    ];
                }
            }
        }

        echo json_encode([
            'status' => 'success',
            'files' => $files
        ]);
    }
}

function handleApiKdocs() {
    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';

        // 调试信息
        error_log("Kdocs API called with action: " . $action);

        switch ($action) {
            case 'sync':
                // 执行金山文档同步
                $phpPath = __DIR__ . '/php/php.exe';
                $scriptPath = __DIR__ . '/QuarkService.php';
                $command = '"' . $phpPath . '" "' . $scriptPath . '" --options sync_kdocs';

                $output = [];
                $returnCode = 0;
                exec($command . ' 2>&1', $output, $returnCode);

                echo json_encode([
                    'status' => $returnCode === 0 ? 'success' : 'error',
                    'output' => implode("\n", $output),
                    'message' => $returnCode === 0 ? '金山文档同步完成' : '金山文档同步失败'
                ]);
                break;

            case 'get_backup':
                // 获取备份文件列表
                $backupDir = __DIR__ . '/kdocs_backup';
                $backups = [];

                if (is_dir($backupDir)) {
                    $files = glob($backupDir . '/*.md');
                    foreach ($files as $file) {
                        $backups[] = [
                            'name' => basename($file),
                            'size' => filesize($file),
                            'modified' => filemtime($file),
                            'path' => str_replace(__DIR__, '', $file)
                        ];
                    }

                    // 按修改时间排序
                    usort($backups, function($a, $b) {
                        return $b['modified'] - $a['modified'];
                    });
                }

                echo json_encode([
                    'status' => 'success',
                    'backups' => $backups
                ]);
                break;

            case 'get_content':
                // 获取备份文件内容
                $filename = $input['filename'] ?? '';
                $backupDir = __DIR__ . '/kdocs_backup';
                $filepath = $backupDir . '/' . basename($filename);

                if (file_exists($filepath)) {
                    $content = file_get_contents($filepath);
                    echo json_encode([
                        'status' => 'success',
                        'content' => $content,
                        'filename' => basename($filename)
                    ]);
                } else {
                    echo json_encode([
                        'status' => 'error',
                        'message' => '文件不存在'
                    ]);
                }
                break;

            case 'get_latest':
                // 获取最新备份文件内容
                $backupDir = __DIR__ . '/kdocs_backup';
                $latestFile = null;
                $latestTime = 0;

                if (is_dir($backupDir)) {
                    $files = glob($backupDir . '/*.md');
                    foreach ($files as $file) {
                        $mtime = filemtime($file);
                        if ($mtime > $latestTime) {
                            $latestTime = $mtime;
                            $latestFile = $file;
                        }
                    }
                }

                if ($latestFile && file_exists($latestFile)) {
                    $content = file_get_contents($latestFile);
                    echo json_encode([
                        'status' => 'success',
                        'content' => $content,
                        'filename' => basename($latestFile)
                    ]);
                } else {
                    echo json_encode(['status' => 'error', 'message' => '没有找到备份文件']);
                }
                break;

            case 'auto_sync':
                // 执行自动同步
                $pythonPath = findPython();

                if (!$pythonPath) {
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'Python环境未安装，系统将自动尝试安装依赖'
                    ]);
                    break;
                }

                // 使用统一的同步脚本
                $scriptPath = __DIR__ . '/kdocs_sync.py';

                if (!file_exists($scriptPath)) {
                    echo json_encode([
                        'status' => 'error',
                        'message' => '同步脚本不存在'
                    ]);
                    break;
                }

                $command = '"' . $pythonPath . '" "' . $scriptPath . '" 2>&1';
                $output = [];
                $returnCode = 0;
                exec($command, $output, $returnCode);

                $outputText = implode("\n", $output);

                echo json_encode([
                    'status' => $returnCode === 0 ? 'success' : 'error',
                    'output' => $outputText,
                    'message' => $returnCode === 0 ?
                        '自动同步完成' :
                        '自动同步失败'
                ]);
                break;

            default:
                echo json_encode(['status' => 'error', 'message' => '未知操作']);
        }
    } else {
        // 获取金山文档配置信息
        echo json_encode([
            'status' => 'success',
            'doc_url' => 'https://kdocs.cn/l/cqLsUjOoGFoV',
            'doc_title' => '火星生活科技短剧库'
        ]);
    }
}

function findPython() {
    $pythonCommands = ['python', 'python3', 'py'];

    foreach ($pythonCommands as $cmd) {
        $output = [];
        $returnCode = 0;
        exec("$cmd --version 2>&1", $output, $returnCode);

        if ($returnCode === 0 && strpos(implode(' ', $output), 'Python') !== false) {
            return $cmd;
        }
    }

    return false;
}
?>
