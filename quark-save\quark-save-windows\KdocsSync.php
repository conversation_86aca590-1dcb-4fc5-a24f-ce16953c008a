<?php

/**
 * 金山文档同步类
 * 用于将夸克网盘分享链接同步到金山文档
 */
class KdocsSync
{
    private $docUrl;
    private $timeout = 30;
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    private $logManager;

    public function __construct($docUrl = '', LogManager $logManager = null)
    {
        $this->docUrl = $docUrl ?: 'https://kdocs.cn/l/cqLsUjOoGFoV';
        $this->logManager = $logManager ?: new LogManager();
    }

    /**
     * 同步分享链接到金山文档
     * @param array $shareData 分享数据 [['name' => '短剧名', 'url' => '分享链接'], ...]
     * @return array [success, message]
     */
    public function syncToKdocs($shareData)
    {
        if (empty($shareData)) {
            return [false, '没有需要同步的数据'];
        }

        try {
            // 记录同步日志
            $this->logSync('开始同步到金山文档', $shareData);

            // 生成同步内容
            $content = $this->generateSyncContent($shareData);
            
            // 保存到本地备份文件
            $backupFile = $this->saveBackup($content);
            
            // 尝试通过API同步（如果有API的话）
            $apiResult = $this->syncViaApi($content);
            
            if ($apiResult[0]) {
                $this->logSync('API同步成功', []);
                return [true, "成功同步 " . count($shareData) . " 条记录到金山文档"];
            } else {
                // API同步失败，生成手动同步指南
                $manualGuide = $this->generateManualGuide($content, $backupFile);
                $this->logSync('API同步失败，已生成手动同步指南', ['guide' => $manualGuide]);
                return [true, "API同步失败，已生成手动同步文件：$backupFile\n请手动复制内容到金山文档"];
            }

        } catch (Exception $e) {
            $this->logSync('同步异常', ['error' => $e->getMessage()]);
            return [false, '同步异常：' . $e->getMessage()];
        }
    }

    /**
     * 生成同步内容
     * @param array $shareData
     * @return string
     */
    private function generateSyncContent($shareData)
    {
        $content = "\n## " . date('Y年m月d日') . " 新增短剧\n\n";
        $content .= "| 序号 | 短剧名称 | 分享链接 | 添加时间 |\n";
        $content .= "|------|----------|----------|----------|\n";

        foreach ($shareData as $index => $item) {
            $num = $index + 1;
            $name = $item['name'] ?? '未知短剧';
            $url = $item['url'] ?? '';
            $time = date('H:i:s');
            
            $content .= "| $num | $name | $url | $time |\n";
        }

        $content .= "\n---\n";
        $content .= "*自动同步时间：" . date('Y-m-d H:i:s') . "*\n\n";

        return $content;
    }

    /**
     * 保存备份文件
     * @param string $content
     * @return string 备份文件路径
     */
    private function saveBackup($content)
    {
        $backupDir = __DIR__ . '/kdocs_backup';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0777, true);
        }

        $filename = 'kdocs_sync_' . date('Y-m-d_H-i-s') . '.md';
        $filepath = $backupDir . '/' . $filename;
        
        file_put_contents($filepath, $content);
        
        return $filepath;
    }

    /**
     * 通过API同步（支持多种同步方式）
     * @param string $content
     * @return array [success, message]
     */
    private function syncViaApi($content)
    {
        // 尝试浏览器自动化同步
        $autoSyncResult = $this->tryAutoSync($content);
        if ($autoSyncResult[0]) {
            return $autoSyncResult;
        }

        // 尝试获取文档信息
        $docInfo = $this->getDocumentInfo();

        if (!$docInfo) {
            return [false, '无法获取文档信息，可能需要登录或权限'];
        }

        // 如果自动化失败，返回手动同步提示
        return [false, '自动同步不可用，已生成手动同步文件'];
    }

    /**
     * 尝试使用自动化同步
     * @param string $content
     * @return array [success, message]
     */
    private function tryAutoSync($content)
    {
        // 优先尝试简化同步
        $simpleResult = $this->trySimpleSync($content);
        if ($simpleResult[0]) {
            return $simpleResult;
        }

        // 如果简化同步失败，尝试完整自动化同步
        $fullResult = $this->tryFullAutoSync($content);
        if ($fullResult[0]) {
            return $fullResult;
        }

        // 都失败了，返回错误信息
        return [false, '自动同步不可用: ' . $simpleResult[1] . '; ' . $fullResult[1]];
    }

    /**
     * 尝试简化同步（复制到剪贴板并打开浏览器）
     * @param string $content
     * @return array [success, message]
     */
    private function trySimpleSync($content)
    {
        try {
            // 检查Python环境
            $pythonPath = $this->findPython();
            if (!$pythonPath) {
                return [false, 'Python环境未安装'];
            }

            // 检查简化同步脚本
            $scriptPath = __DIR__ . '/kdocs_simple_sync.py';
            if (!file_exists($scriptPath)) {
                return [false, '简化同步脚本不存在'];
            }

            // 执行Python简化同步脚本
            $command = sprintf(
                '"%s" "%s" --content "%s" 2>&1',
                $pythonPath,
                $scriptPath,
                addslashes($content)
            );

            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                $this->logSync('简化自动同步成功', ['output' => implode("\n", $output)]);
                return [true, '简化自动同步成功，内容已复制到剪贴板并打开文档'];
            } else {
                $this->logSync('简化自动同步失败', ['output' => implode("\n", $output), 'return_code' => $returnCode]);
                return [false, '简化自动同步失败: ' . implode("\n", $output)];
            }

        } catch (Exception $e) {
            $this->logSync('简化自动同步异常', ['error' => $e->getMessage()]);
            return [false, '简化自动同步异常: ' . $e->getMessage()];
        }
    }

    /**
     * 尝试完整自动化同步（使用Selenium）
     * @param string $content
     * @return array [success, message]
     */
    private function tryFullAutoSync($content)
    {
        try {
            // 检查Python环境
            $pythonPath = $this->findPython();
            if (!$pythonPath) {
                return [false, 'Python环境未安装'];
            }

            // 检查完整自动同步脚本
            $scriptPath = __DIR__ . '/kdocs_auto_sync.py';
            if (!file_exists($scriptPath)) {
                return [false, '完整自动同步脚本不存在'];
            }

            // 执行Python完整自动同步脚本
            $command = sprintf(
                '"%s" "%s" --content "%s" --headless 2>&1',
                $pythonPath,
                $scriptPath,
                addslashes($content)
            );

            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                $this->logSync('完整自动化同步成功', ['output' => implode("\n", $output)]);
                return [true, '完整自动化同步成功'];
            } else {
                $this->logSync('完整自动化同步失败', ['output' => implode("\n", $output), 'return_code' => $returnCode]);
                return [false, '完整自动化同步失败: ' . implode("\n", $output)];
            }

        } catch (Exception $e) {
            $this->logSync('完整自动化同步异常', ['error' => $e->getMessage()]);
            return [false, '完整自动化同步异常: ' . $e->getMessage()];
        }
    }

    /**
     * 查找Python可执行文件
     * @return string|false
     */
    private function findPython()
    {
        $pythonCommands = ['python', 'python3', 'py'];

        foreach ($pythonCommands as $cmd) {
            $output = [];
            $returnCode = 0;
            exec("$cmd --version 2>&1", $output, $returnCode);

            if ($returnCode === 0 && strpos(implode(' ', $output), 'Python') !== false) {
                return $cmd;
            }
        }

        return false;
    }

    /**
     * 获取文档信息
     * @return array|false
     */
    private function getDocumentInfo()
    {
        $headers = [
            'User-Agent: ' . $this->userAgent,
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding: gzip, deflate',
            'Connection: keep-alive',
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->docUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $response) {
            // 简单检查是否能访问文档
            if (strpos($response, '金山文档') !== false || strpos($response, 'kdocs') !== false) {
                return ['accessible' => true, 'title' => '火星生活科技短剧库'];
            }
        }

        return false;
    }

    /**
     * 生成手动同步指南
     * @param string $content
     * @param string $backupFile
     * @return string
     */
    private function generateManualGuide($content, $backupFile)
    {
        $guide = "=== 金山文档手动同步指南 ===\n\n";
        $guide .= "1. 打开金山文档：{$this->docUrl}\n";
        $guide .= "2. 找到文档末尾或合适的位置\n";
        $guide .= "3. 复制以下内容并粘贴到文档中：\n\n";
        $guide .= "--- 复制内容开始 ---\n";
        $guide .= $content;
        $guide .= "--- 复制内容结束 ---\n\n";
        $guide .= "4. 保存文档\n\n";
        $guide .= "备份文件位置：$backupFile\n";
        $guide .= "生成时间：" . date('Y-m-d H:i:s') . "\n";

        // 保存指南到文件
        $guideFile = dirname($backupFile) . '/manual_guide_' . date('Y-m-d_H-i-s') . '.txt';
        file_put_contents($guideFile, $guide);

        return $guideFile;
    }

    /**
     * 记录同步日志
     * @param string $message
     * @param array $data
     */
    private function logSync($message, $data = [])
    {
        $logFile = $this->logManager->getSystemLogPath('kdocs_sync');
        $logEntry = date('Y-m-d H:i:s') . " - $message";
        
        if (!empty($data)) {
            $logEntry .= " - " . json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        
        $logEntry .= "\n";
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * 从分享日志文件读取分享数据
     * @param string $shareLogFile
     * @return array
     */
    public function parseShareLog($shareLogFile)
    {
        if (!file_exists($shareLogFile)) {
            return [];
        }

        $shareData = [];
        $lines = file($shareLogFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        foreach ($lines as $line) {
            $parts = explode('>>>', $line);
            if (count($parts) >= 2) {
                $shareData[] = [
                    'name' => trim($parts[0]),
                    'url' => trim($parts[1])
                ];
            }
        }

        return $shareData;
    }

    /**
     * 设置文档URL
     * @param string $url
     */
    public function setDocUrl($url)
    {
        $this->docUrl = $url;
    }

    /**
     * 获取当前文档URL
     * @return string
     */
    public function getDocUrl()
    {
        return $this->docUrl;
    }
}
?>
